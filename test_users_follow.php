<?php
/**
 * سكريبت اختبار دالة usersFollowAddOrRemove
 * 
 * كيفية الاستخدام:
 * php test_users_follow.php
 */

// تحديد المسار الأساسي للمشروع
$baseUrl = 'http://localhost:8000'; // غيّر هذا حسب domain الخاص بك
$customerToken = 'YOUR_CUSTOMER_TOKEN_HERE'; // ضع التوكن الخاص بك هنا

// بيانات الاختبار
$testData = [
    'follower_id' => 1,    // ID المستخدم المتابِع
    'followed_id' => 2,    // ID المستخدم المُتابَع
];

echo "🧪 بدء اختبار دالة usersFollowAddOrRemove\n";
echo "=====================================\n\n";

/**
 * دالة لإرسال طلب HTTP
 */
function sendRequest($url, $data, $token) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json',
        'Authorization: Bearer ' . $token
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return [
        'http_code' => $httpCode,
        'response' => json_decode($response, true)
    ];
}

/**
 * اختبار 1: متابعة مستخدم جديد
 */
echo "📝 اختبار 1: متابعة مستخدم جديد\n";
echo "--------------------------------\n";

$url = $baseUrl . '/api/auth/usersFollowAddOrRemove';
$data = ['customer_id' => $testData['followed_id']];

$result = sendRequest($url, $data, $customerToken);

echo "HTTP Code: " . $result['http_code'] . "\n";
echo "Response: " . json_encode($result['response'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";

if ($result['response']['status'] && isset($result['response']['result']['isFollow']) && $result['response']['result']['isFollow']) {
    echo "✅ نجح: تم إضافة المتابعة\n\n";
} else {
    echo "❌ فشل: لم يتم إضافة المتابعة\n\n";
}

sleep(1); // انتظار ثانية واحدة

/**
 * اختبار 2: إلغاء المتابعة (نفس الطلب)
 */
echo "📝 اختبار 2: إلغاء المتابعة\n";
echo "----------------------------\n";

$result = sendRequest($url, $data, $customerToken);

echo "HTTP Code: " . $result['http_code'] . "\n";
echo "Response: " . json_encode($result['response'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";

if ($result['response']['status'] && isset($result['response']['result']['remove']) && $result['response']['result']['remove']) {
    echo "✅ نجح: تم إلغاء المتابعة\n\n";
} else {
    echo "❌ فشل: لم يتم إلغاء المتابعة\n\n";
}

sleep(1);

/**
 * اختبار 3: محاولة متابعة النفس
 */
echo "📝 اختبار 3: محاولة متابعة النفس\n";
echo "--------------------------------\n";

$data = ['customer_id' => $testData['follower_id']]; // نفس ID المستخدم الحالي

$result = sendRequest($url, $data, $customerToken);

echo "HTTP Code: " . $result['http_code'] . "\n";
echo "Response: " . json_encode($result['response'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";

if (!$result['response']['status'] && strpos($result['response']['msg'], 'لا يمكنك متابعة نفسك') !== false) {
    echo "✅ نجح: تم منع متابعة النفس\n\n";
} else {
    echo "❌ فشل: لم يتم منع متابعة النفس\n\n";
}

sleep(1);

/**
 * اختبار 4: customer_id غير موجود
 */
echo "📝 اختبار 4: customer_id غير موجود\n";
echo "--------------------------------\n";

$data = ['customer_id' => 99999]; // ID غير موجود

$result = sendRequest($url, $data, $customerToken);

echo "HTTP Code: " . $result['http_code'] . "\n";
echo "Response: " . json_encode($result['response'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";

if (!$result['response']['status'] && strpos($result['response']['msg'], 'invalid') !== false) {
    echo "✅ نجح: تم رفض customer_id غير موجود\n\n";
} else {
    echo "❌ فشل: لم يتم رفض customer_id غير موجود\n\n";
}

sleep(1);

/**
 * اختبار 5: بدون customer_id
 */
echo "📝 اختبار 5: بدون customer_id\n";
echo "-----------------------------\n";

$data = []; // بدون customer_id

$result = sendRequest($url, $data, $customerToken);

echo "HTTP Code: " . $result['http_code'] . "\n";
echo "Response: " . json_encode($result['response'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";

if (!$result['response']['status'] && strpos($result['response']['msg'], 'required') !== false) {
    echo "✅ نجح: تم رفض الطلب بدون customer_id\n\n";
} else {
    echo "❌ فشل: لم يتم رفض الطلب بدون customer_id\n\n";
}

echo "🎉 انتهى الاختبار!\n";
echo "================\n\n";

echo "📋 ملاحظات:\n";
echo "- تأكد من تغيير \$baseUrl إلى domain الصحيح\n";
echo "- تأكد من وضع customer token صحيح\n";
echo "- تأكد من وجود المستخدمين في قاعدة البيانات\n";
echo "- راجع logs Laravel للمزيد من التفاصيل: tail -f storage/logs/laravel.log\n";
?>
