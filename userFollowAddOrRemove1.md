# 🏪 شرح مفصل لـ API: followAddOrRemove - نظام متابعة المتاجر

## 📋 **نظرة عامة على المشكلة**

أنت تواجه مشكلة في عدم زيادة عدد المتابعين عند قيام المستخدم العادي بمتابعة متجر معين. هذا الـ API مخصص لمتابعة المتاجر وليس المستخدمين.

### **🔗 معلومات الـ API:**
- **المسار**: `POST /api/auth/followAddOrRemove`
- **المصادقة**: مطلوبة (`auth:customers`)
- **Controller**: `UserApiController`
- **Method**: `followAddOrRemove()`
- **الغرض**: متابعة/إلغاء متابعة المتاجر من قبل العملاء

---

## 🔍 **تتبع الـ API خطوة بخطوة**

### **1️⃣ تعريف Route**
```php
// في routes/api.php (السطر 211)
Route::group(['middleware' => ['auth:customers'],'prefix'=>'auth'], function () {
    Route::post('/followAddOrRemove', [\App\Http\Controllers\Api\UserApiController::class,'followAddOrRemove']);
});
```

**الملاحظات:**
- يقع داخل `Route::group(['middleware' => ['auth:customers'])`
- يتطلب مصادقة العميل (وليس المتجر)
- يستخدم HTTP Method: POST

---

### **2️⃣ الكود الكامل للدالة**
```php
public function followAddOrRemove(Request $request)
{
    // 1. التحقق من صحة البيانات
    $this->validate(
        $request,
        [
            "store_id" => "required|exists:stores,id",
        ]
    );

    // 2. البحث عن علاقة المتابعة الموجودة
    $follower = Follower::where("store_id", $request->store_id)
        ->where("customer_id", $this->CURRENT_USER->id)
        ->first();

    // 3. إذا كانت المتابعة موجودة - إلغاء المتابعة
    if($follower){
        $follower->delete();
        $data = [
            'remove' => true,
            'isFollow' => false,
        ];
        return $this->sendResponse($data, \Lang::get("lang.following_deleted"));
    }
    
    // 4. إذا لم تكن موجودة - إضافة المتابعة
    $create = Follower::create([
        "store_id" => (int) $request->store_id,
        "customer_id" => $this->CURRENT_USER->id
    ]);
    
    $create = (array)$create->toArray();
    $create["store_id"] = (int) $create["store_id"];
    $create["isFollow"] = true;

    // 5. إرسال إشعار للمتجر
    // [كود الإشعارات...]
    
    return $this->sendResponse($create, \Lang::get("lang.following_added"));
}
```

---

## 🔍 **تحليل مفصل لكل خطوة**

### **خطوة 1: التحقق من صحة البيانات (Validation)**
```php
$this->validate(
    $request,
    [
        "store_id" => "required|exists:stores,id",
    ]
);
```

**الشرح:**
- `required`: الحقل مطلوب ولا يمكن أن يكون فارغاً
- `exists:stores,id`: يتحقق أن الـ store_id موجود فعلاً في جدول stores

**مثال على الطلب:**
```json
POST /api/auth/followAddOrRemove
{
    "store_id": 123
}
```

**أخطاء محتملة:**
```json
// إذا لم يتم إرسال store_id
{
    "status": false,
    "msg": "The store id field is required.",
    "result": null
}

// إذا كان store_id غير موجود
{
    "status": false,
    "msg": "The selected store id is invalid.",
    "result": null
}
```

---

### **خطوة 2: البحث عن علاقة المتابعة**
```php
$follower = Follower::where("store_id", $request->store_id)
    ->where("customer_id", $this->CURRENT_USER->id)
    ->first();
```

**الشرح:**
- يبحث في جدول `followers`
- `store_id`: المتجر المراد متابعته
- `customer_id`: العميل الحالي (المتابِع)
- `first()`: يجلب أول نتيجة أو `null` إذا لم توجد

**مثال على الاستعلام المولد:**
```sql
SELECT * FROM followers 
WHERE store_id = 123 
  AND customer_id = 456 
LIMIT 1;
```

---

### **خطوة 3: إلغاء المتابعة (إذا كانت موجودة)**
```php
if($follower){
    $follower->delete();
    $data = [
        'remove' => true,
        'isFollow' => false,
    ];
    return $this->sendResponse($data, \Lang::get("lang.following_deleted"));
}
```

**الشرح:**
- إذا وُجدت علاقة متابعة، يتم حذفها
- يرجع استجابة تؤكد إلغاء المتابعة

**استجابة إلغاء المتابعة:**
```json
{
    "status": true,
    "msg": "تم إلغاء المتابعة",
    "result": {
        "remove": true,
        "isFollow": false
    }
}
```

---

### **خطوة 4: إضافة المتابعة (إذا لم تكن موجودة)**
```php
$create = Follower::create([
    "store_id" => (int) $request->store_id,
    "customer_id" => $this->CURRENT_USER->id
]);

$create = (array)$create->toArray();
$create["store_id"] = (int) $create["store_id"];
$create["isFollow"] = true;
```

**الشرح:**
- ينشئ سجل جديد في جدول `followers`
- يحول النتيجة إلى مصفوفة
- يضيف حقل `isFollow: true`
- يتأكد من أن store_id هو integer

**استجابة إضافة المتابعة:**
```json
{
    "status": true,
    "msg": "تم إضافة المتابعة",
    "result": {
        "id": 1,
        "store_id": 123,
        "customer_id": 456,
        "created_at": "2024-01-01T10:00:00.000000Z",
        "updated_at": "2024-01-01T10:00:00.000000Z",
        "notifications": 0,
        "isFollow": true
    }
}
```

---

## 🗄️ **هيكل قاعدة البيانات**

### **جدول followers:**
```sql
CREATE TABLE followers (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    store_id INT NULL,                    -- المتجر المُتابَع
    customer_id INT NULL,                 -- العميل المتابِع
    notifications INT DEFAULT 0,          -- حالة الإشعارات
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

### **العلاقات في Models:**
```php
// في Store.php
public function follower(){
    return $this->belongsToMany(Customer::class, "followers", "store_id", "customer_id")->distinct();
}

// في Follower.php
public function customer(){
    return $this->belongsTo(Customer::class);
}
```

---

## 🎯 **مثال عملي للاستخدام**

### **مثال 1: متابعة متجر جديد**
```bash
curl -X POST "http://localhost:8000/api/auth/followAddOrRemove" \
  -H "Authorization: Bearer YOUR_CUSTOMER_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"store_id": 123}'
```

### **مثال 2: إلغاء متابعة متجر**
```bash
# نفس الطلب السابق (toggle behavior)
curl -X POST "http://localhost:8000/api/auth/followAddOrRemove" \
  -H "Authorization: Bearer YOUR_CUSTOMER_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"store_id": 123}'
```

---

## 🚨 **تشخيص المشكلة المحتملة**

### **المشاكل الشائعة:**

#### **1️⃣ مشكلة المصادقة:**
```bash
# تأكد من أن التوكن صحيح ومن نوع customers وليس store
Authorization: Bearer {customer_token}
```

#### **2️⃣ مشكلة في البيانات المرسلة:**
```json
// تأكد من إرسال store_id وليس customer_id
{
    "store_id": 123  // ✅ صحيح
}

// وليس
{
    "customer_id": 123  // ❌ خطأ - هذا للـ API الآخر
}
```

#### **3️⃣ مشكلة في قاعدة البيانات:**
```sql
-- تحقق من وجود الجدول
SHOW TABLES LIKE 'followers';

-- تحقق من هيكل الجدول
DESCRIBE followers;

-- تحقق من البيانات
SELECT * FROM followers WHERE store_id = 123;
```

#### **4️⃣ مشكلة في الإشعارات:**
هناك خطأ في كود الإشعارات قد يسبب فشل العملية:
```php
// السطر 574 - خطأ في الكود
$customersDetails->first()->username  // ❌ خطأ

// يجب أن يكون
$customersDetails->username  // ✅ صحيح
```

---

## 🔧 **حلول المشاكل**

### **1️⃣ إصلاح خطأ الإشعارات:**
```php
// في السطر 574، غيّر من:
$customersDetails->first()->username . ' follow you'

// إلى:
$customersDetails->username . ' follow you'
```

### **2️⃣ إضافة تحقق من صحة التوكن:**
```php
// في بداية الدالة
if (!$this->CURRENT_USER) {
    return $this->sendError('User not authenticated', 401);
}
```

### **3️⃣ إضافة معالجة الأخطاء:**
```php
try {
    // كود الإشعارات
} catch (\Exception $e) {
    \Log::error('Notification failed: ' . $e->getMessage());
    // لا توقف العملية، فقط سجل الخطأ
}
```

---

## 📱 **كيفية الاستخدام في التطبيق**

### **JavaScript/React Example:**
```javascript
const followStore = async (storeId) => {
    try {
        const response = await fetch('/api/auth/followAddOrRemove', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('customer_token')}`,
                'X-Client-Language': 'ar'
            },
            body: JSON.stringify({
                store_id: storeId
            })
        });

        const data = await response.json();

        if (data.status) {
            if (data.result.isFollow) {
                console.log('تم متابعة المتجر');
                updateFollowButton(storeId, true);
            } else {
                console.log('تم إلغاء متابعة المتجر');
                updateFollowButton(storeId, false);
            }
        } else {
            console.error('خطأ:', data.msg);
        }
    } catch (error) {
        console.error('خطأ في الشبكة:', error);
    }
};
```

### **Flutter/Dart Example:**
```dart
Future<bool> followStore(int storeId) async {
  try {
    final response = await http.post(
      Uri.parse('${baseUrl}/api/auth/followAddOrRemove'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ${await getCustomerToken()}',
        'X-Client-Language': 'ar',
      },
      body: jsonEncode({
        'store_id': storeId,
      }),
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      if (data['status']) {
        return data['result']['isFollow'] ?? false;
      }
    }
    return false;
  } catch (e) {
    print('خطأ في متابعة المتجر: $e');
    return false;
  }
}
```

---

## 🧪 **اختبار الـ API**

### **1️⃣ اختبار باستخدام Postman:**
```
POST {{base_url}}/api/auth/followAddOrRemove
Authorization: Bearer {{customer_token}}
Content-Type: application/json

{
    "store_id": 1
}
```

### **2️⃣ اختبار من خلال Laravel Tinker:**
```php
// في terminal
php artisan tinker

// اختبار المتابعة
$customer = \App\Models\Customer::find(1);
auth('customers')->login($customer);

$response = app()->call('App\Http\Controllers\Api\UserApiController@followAddOrRemove', [
    'request' => new \Illuminate\Http\Request(['store_id' => 1])
]);

dd($response);
```

### **3️⃣ فحص قاعدة البيانات:**
```sql
-- فحص المتابعات
SELECT * FROM followers WHERE customer_id = 1;

-- فحص متابعي متجر معين
SELECT c.username, f.created_at 
FROM followers f 
JOIN customers c ON f.customer_id = c.id 
WHERE f.store_id = 1;

-- عدد متابعي كل متجر
SELECT s.name, COUNT(f.id) as followers_count
FROM stores s
LEFT JOIN followers f ON s.id = f.store_id
GROUP BY s.id, s.name;
```

---

## ✅ **خطوات حل المشكلة**

1. **تأكد من استخدام التوكن الصحيح** (customer token وليس store token)
2. **تأكد من إرسال store_id وليس customer_id**
3. **إصلح خطأ الإشعارات في السطر 574**
4. **تحقق من وجود جدول followers في قاعدة البيانات**
5. **اختبر الـ API باستخدام Postman أو curl**
6. **فحص logs للأخطاء**

هذا الشرح يجب أن يساعدك في حل مشكلة عدم زيادة عدد المتابعين للمتاجر!

---

## 🔍 **تشخيص متقدم للمشكلة**

### **فحص الكود الحالي للأخطاء:**

#### **خطأ رقم 1: في كود الإشعارات (السطر 574)**
```php
// الكود الحالي (خطأ):
\Notification::send($StoreDetails,
    new \App\Notifications\GeneralNotification(
        $customersDetails->first()->username . ' follow you',  // ❌ خطأ
        'follow_request',
        $data
    )
);

// الكود الصحيح:
\Notification::send($StoreDetails,
    new \App\Notifications\GeneralNotification(
        $customersDetails->username . ' follow you',  // ✅ صحيح
        'follow_request',
        $data
    )
);
```

#### **خطأ رقم 2: فحص FCM Token فارغ**
```php
// السطر 553-555 - قد يوقف العملية إذا لم يكن هناك FCM token
if (empty($fcmTokens)) {
    return $this->sendError('No valid FCM tokens found for the store', 400);
}
```

**المشكلة:** إذا لم يكن للمتجر FCM token، ستفشل العملية بالكامل ولن يتم حفظ المتابعة.

---

## 🛠️ **الحلول المقترحة**

### **حل 1: إصلاح خطأ الإشعارات**
```php
// استبدل السطر 574 في UserApiController.php
// من:
$customersDetails->first()->username . ' follow you'

// إلى:
$customersDetails->username . ' follow you'
```

### **حل 2: جعل الإشعارات اختيارية**
```php
// استبدل الكود من السطر 553-555
// من:
if (empty($fcmTokens)) {
    return $this->sendError('No valid FCM tokens found for the store', 400);
}

// إلى:
if (empty($fcmTokens)) {
    \Log::warning('No FCM token found for store: ' . $request->store_id);
    // لا توقف العملية، فقط سجل تحذير
} else {
    // أرسل الإشعار فقط إذا كان هناك token
    try {
        $factory = (new Factory())->withServiceAccount(base_path(env('FIREBASE_CREDENTIALS')));
        $messaging = $factory->createMessaging();
        $notification = Notification::create("New Follower", $customersDetails->username . ' follow you');
        $message = CloudMessage::withTarget('token', $StoreDetails->fcm_token)->withNotification($notification);
        $messaging->sendMulticast($message, [$fcmTokens]);
    } catch (\Exception $e) {
        \Log::error('Failed to send notification: ' . $e->getMessage());
    }
}
```

### **حل 3: إضافة معالجة شاملة للأخطاء**
```php
public function followAddOrRemove(Request $request)
{
    try {
        $this->validate($request, [
            "store_id" => "required|exists:stores,id",
        ]);

        $follower = Follower::where("store_id", $request->store_id)
            ->where("customer_id", $this->CURRENT_USER->id)
            ->first();

        if($follower){
            $follower->delete();
            $data = [
                'remove' => true,
                'isFollow' => false,
            ];
            return $this->sendResponse($data, \Lang::get("lang.following_deleted"));
        }

        $create = Follower::create([
            "store_id" => (int) $request->store_id,
            "customer_id" => $this->CURRENT_USER->id
        ]);

        $create = (array)$create->toArray();
        $create["store_id"] = (int) $create["store_id"];
        $create["isFollow"] = true;

        // إرسال الإشعارات (اختياري)
        $this->sendFollowNotification($request->store_id);

        return $this->sendResponse($create, \Lang::get("lang.following_added"));

    } catch (\Exception $e) {
        \Log::error('Follow store error: ' . $e->getMessage());
        return $this->sendError('حدث خطأ أثناء المتابعة', 500);
    }
}

private function sendFollowNotification($storeId)
{
    try {
        $StoreDetails = Store::find($storeId);
        $customersDetails = Customer::find($this->CURRENT_USER->id);

        if (!$StoreDetails || !$customersDetails) {
            return;
        }

        // إرسال إشعار داخل التطبيق
        $data = [
            'id' => $customersDetails->id,
            'username' => $customersDetails->username . ' follow you',
            'type' => 'follow_request'
        ];

        \Notification::send($StoreDetails,
            new \App\Notifications\GeneralNotification(
                $customersDetails->username . ' follow you',
                'follow_request',
                $data
            )
        );

        // إرسال Push Notification (إذا كان هناك FCM token)
        if (!empty($StoreDetails->fcm_token)) {
            $factory = (new Factory())->withServiceAccount(base_path(env('FIREBASE_CREDENTIALS')));
            $messaging = $factory->createMessaging();
            $notification = Notification::create("New Follower", $customersDetails->username . ' follow you');
            $message = CloudMessage::withTarget('token', $StoreDetails->fcm_token)->withNotification($notification);
            $messaging->send($message);
        }

    } catch (\Exception $e) {
        \Log::error('Failed to send follow notification: ' . $e->getMessage());
        // لا توقف العملية الأساسية
    }
}
```

---

## 📊 **فحص البيانات في قاعدة البيانات**

### **1️⃣ فحص جدول followers:**
```sql
-- تحقق من وجود الجدول
SHOW TABLES LIKE 'followers';

-- فحص هيكل الجدول
DESCRIBE followers;

-- فحص البيانات الموجودة
SELECT * FROM followers LIMIT 10;

-- فحص متابعة معينة
SELECT * FROM followers
WHERE store_id = 123 AND customer_id = 456;
```

### **2️⃣ فحص عدد المتابعين لكل متجر:**
```sql
-- عدد المتابعين لكل متجر
SELECT
    s.id,
    s.name,
    COUNT(f.id) as followers_count
FROM stores s
LEFT JOIN followers f ON s.id = f.store_id
GROUP BY s.id, s.name
ORDER BY followers_count DESC;

-- متابعي متجر معين
SELECT
    c.id,
    c.username,
    c.email,
    f.created_at as followed_at
FROM followers f
JOIN customers c ON f.customer_id = c.id
WHERE f.store_id = 123
ORDER BY f.created_at DESC;
```

### **3️⃣ فحص المتاجر التي يتابعها عميل معين:**
```sql
SELECT
    s.id,
    s.name,
    s.category_id,
    f.created_at as followed_at
FROM followers f
JOIN stores s ON f.store_id = s.id
WHERE f.customer_id = 456
ORDER BY f.created_at DESC;
```

---

## 🧪 **اختبار شامل للـ API**

### **اختبار 1: متابعة متجر جديد**
```bash
curl -X POST "http://your-domain.com/api/auth/followAddOrRemove" \
  -H "Authorization: Bearer YOUR_CUSTOMER_TOKEN" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "store_id": 1
  }'
```

**النتيجة المتوقعة:**
```json
{
    "status": true,
    "msg": "تم إضافة المتابعة",
    "result": {
        "id": 1,
        "store_id": 1,
        "customer_id": 123,
        "notifications": 0,
        "created_at": "2024-01-01T10:00:00.000000Z",
        "updated_at": "2024-01-01T10:00:00.000000Z",
        "isFollow": true
    }
}
```

### **اختبار 2: إلغاء متابعة متجر**
```bash
# نفس الطلب السابق
curl -X POST "http://your-domain.com/api/auth/followAddOrRemove" \
  -H "Authorization: Bearer YOUR_CUSTOMER_TOKEN" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "store_id": 1
  }'
```

**النتيجة المتوقعة:**
```json
{
    "status": true,
    "msg": "تم إلغاء المتابعة",
    "result": {
        "remove": true,
        "isFollow": false
    }
}
```

---

## 🔧 **أدوات التشخيص**

### **1️⃣ إضافة Logging للتشخيص:**
```php
// أضف في بداية دالة followAddOrRemove
\Log::info('Follow API called', [
    'customer_id' => $this->CURRENT_USER->id,
    'store_id' => $request->store_id,
    'timestamp' => now()
]);

// أضف بعد إنشاء المتابعة
\Log::info('Follow created successfully', [
    'follower_id' => $create['id'],
    'customer_id' => $this->CURRENT_USER->id,
    'store_id' => $request->store_id
]);
```

### **2️⃣ فحص الـ Logs:**
```bash
# فحص logs Laravel
tail -f storage/logs/laravel.log

# البحث عن أخطاء معينة
grep -i "follow" storage/logs/laravel.log
grep -i "error" storage/logs/laravel.log
```

### **3️⃣ استخدام Laravel Telescope:**
```bash
# تثبيت Telescope للمراقبة
composer require laravel/telescope --dev
php artisan telescope:install
php artisan migrate

# الوصول للوحة المراقبة
http://your-domain.com/telescope
```

---

## ⚡ **نصائح لتحسين الأداء**

### **1️⃣ إضافة Database Indexes:**
```sql
-- إضافة فهارس لتحسين الأداء
CREATE INDEX idx_followers_store_id ON followers(store_id);
CREATE INDEX idx_followers_customer_id ON followers(customer_id);
CREATE INDEX idx_followers_store_customer ON followers(store_id, customer_id);
```

### **2️⃣ استخدام Queue للإشعارات:**
```php
// بدلاً من إرسال الإشعار مباشرة
dispatch(new SendStoreFollowNotification($this->CURRENT_USER, $StoreDetails));
```

### **3️⃣ إضافة Cache للعدادات:**
```php
// تحديث عداد المتابعين في Cache
Cache::forget("store_followers_count_{$request->store_id}");
Cache::forget("customer_following_stores_count_{$this->CURRENT_USER->id}");
```

---

## 📱 **التكامل مع التطبيق**

### **في React Native:**
```javascript
import AsyncStorage from '@react-native-async-storage/async-storage';

const followStore = async (storeId) => {
  try {
    const token = await AsyncStorage.getItem('customer_token');

    const response = await fetch(`${API_BASE_URL}/api/auth/followAddOrRemove`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        'Accept': 'application/json',
      },
      body: JSON.stringify({
        store_id: storeId
      })
    });

    const data = await response.json();

    if (data.status) {
      return {
        success: true,
        isFollowing: data.result.isFollow || false,
        message: data.msg
      };
    } else {
      throw new Error(data.msg);
    }
  } catch (error) {
    console.error('Follow store error:', error);
    return {
      success: false,
      error: error.message
    };
  }
};
```

### **في Flutter:**
```dart
class StoreFollowService {
  static const String baseUrl = 'https://your-domain.com';

  static Future<Map<String, dynamic>> followStore(int storeId) async {
    try {
      final token = await getCustomerToken();

      final response = await http.post(
        Uri.parse('$baseUrl/api/auth/followAddOrRemove'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
          'Accept': 'application/json',
        },
        body: jsonEncode({
          'store_id': storeId,
        }),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['status']) {
        return {
          'success': true,
          'isFollowing': data['result']['isFollow'] ?? false,
          'message': data['msg']
        };
      } else {
        throw Exception(data['msg'] ?? 'Unknown error');
      }
    } catch (e) {
      return {
        'success': false,
        'error': e.toString()
      };
    }
  }
}
```

---

## 🎯 **خطة العمل لحل المشكلة**

### **الخطوة 1: التشخيص الأولي**
1. تحقق من أن التطبيق يرسل الطلب للمسار الصحيح: `/api/auth/followAddOrRemove`
2. تأكد من أن التوكن المستخدم هو customer token وليس store token
3. تحقق من أن البيانات المرسلة تحتوي على `store_id` وليس `customer_id`

### **الخطوة 2: فحص قاعدة البيانات**
1. تأكد من وجود جدول `followers`
2. فحص البيانات الموجودة في الجدول
3. تحقق من أن العلاقات في Models صحيحة

### **الخطوة 3: إصلاح الأخطاء**
1. إصلح خطأ الإشعارات في السطر 574
2. اجعل الإشعارات اختيارية لتجنب فشل العملية
3. أضف معالجة شاملة للأخطاء

### **الخطوة 4: الاختبار**
1. اختبر الـ API باستخدام Postman
2. اختبر من التطبيق مباشرة
3. فحص البيانات في قاعدة البيانات بعد كل اختبار

### **الخطوة 5: المراقبة**
1. أضف Logging للمراقبة
2. استخدم Laravel Telescope إذا أمكن
3. راقب الـ logs للتأكد من عدم وجود أخطاء

---

## 🔍 **الخلاصة**

المشكلة الأساسية على الأرجح تكمن في:

1. **خطأ في كود الإشعارات** (السطر 574) يسبب فشل العملية
2. **فحص FCM Token الصارم** يوقف العملية إذا لم يكن هناك token
3. **عدم معالجة الأخطاء** بشكل صحيح

بتطبيق الحلول المقترحة أعلاه، يجب أن تحل المشكلة وتعمل متابعة المتاجر بشكل صحيح.
