<?php

namespace App\Console\Commands;

use App\Models\Customer;
use App\Models\DrawCandidate;
use App\Models\Store;
use App\Services\SendNotification;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class DrawSystem extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'draw:run';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'this command run draw system ';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        set_time_limit(0);
        ini_set('max_execution_time', 0);
        ini_set('memory_limit', -1);

        /*
        $draw= \App\Models\SystemDraw::find(158);
           SendNotification::drawSystemStoreNotification($draw,91,50);
           file_put_contents("draw.log",date("Y-m-d H:i:s").PHP_EOL,FILE_APPEND);

           return 0;
        */

        $endtDate=\Carbon\Carbon::now()->format('Y-m-d H:i:s');

        file_put_contents("draw.log","date now => ".$endtDate.PHP_EOL,FILE_APPEND);
        // $endtDate=\Carbon\Carbon::now()->addSeconds(60)->format('Y-m-d H:i:s');
        // file_put_contents("draw.log","date now+60 seconds => ".$endtDate.PHP_EOL,FILE_APPEND);
//        dd('test');
        /**
         * this is 
         */
        // sleep(55);

        $execution_date=\Carbon\Carbon::now()->format('Y-m-d H:i:s');

        file_put_contents("draw.log","execution time  => ".$execution_date.PHP_EOL,FILE_APPEND);

        $draws = \App\Models\SystemDraw::where("is_end",false)
            ->where('date', '<=', $endtDate)->get();

        //  die();

        foreach ($draws as $draw){
            if ($draw->store_id){
                $getAllCandidate = DrawCandidate::
                where("store_id",$draw->store_id)
                    ->where("store_draw",false)->get();
                if ($getAllCandidate->isNotEmpty()){
                    $getAllCandidate =$getAllCandidate ->random();
                    $getAllCandidate->store_draw = true;
                    $getAllCandidate->save();

                    $storeFind = Store::find($getAllCandidate->store_id);
                    $customerFind = Customer::find($getAllCandidate->customer_id);
                    $draw->winner_store_id = $storeFind->id;
                    $draw->winner_customer_id = $customerFind->id;
                    $draw->save();
                    SendNotification::drawSystemStoreNotification($draw,$customerFind->id,$storeFind->id);

                }
                DrawCandidate::
                where("store_id",$draw->store_id)
                    ->where("store_draw",false)
                    ->update(["store_draw"=>true]);

            }
            else{

                $getAllCandidateAll = DrawCandidate::where("system_draw", false)
                    // ->whereHas("store", function ($query){ //ramallah
                    //     $query->where("governorate_id" , 208);
                    // })
                    // ->where(function ($query){
                    //     $query->whereNotNull("order_id")
                    //         ->orWhereHas("QRRequest",function ($q){
                    //             $q->where("status","Approved");
                    //         });

                    // })
                    ->get();


                //select unique candidates qrouped by date

                $getAllCandidateAll = $getAllCandidateAll->map(function ($item) {
                    return [
                        'store_id' => $item->store_id,
                        'customer_id' => $item->customer_id,
                        'date' => $item->created_at->toDateString()

                    ];
                })->unique();


                if ($getAllCandidateAll->isNotEmpty()){

                    $winner =    $getAllCandidateAll->random();

                    $storeFind = Store::find($winner["store_id"]);
                    $customerFind = Customer::find($winner["customer_id"]);
                    $draw->winner_store_id = $storeFind->id;
                    $draw->winner_customer_id = $customerFind->id;
                    $draw->save();
                    SendNotification::drawSystemNotification($draw,$customerFind->id,$storeFind->id);
                }

                $fcm= new \App\Services\PushNotification();

                $msg = "تم انتهاء السحب على جائزة ".$draw->prize_name." اضغط لمعرفة الفائزين . ";

                $data=[
                    'title' => $msg,
                    'body' => $msg,
                    'id'=>null,
                    'type'=>'main',
                ];


                DrawCandidate::where("system_draw",false)->update(["system_draw"=>true]);

                $fcm->sendMultiCast($data);


            }


            file_put_contents("draw.log","draw date => ".$draw->date.PHP_EOL,FILE_APPEND);

            $draw->is_end=true;
            $draw->save();

        }

        //$admins = DrawSystem::;

        return 0;
    }
}
