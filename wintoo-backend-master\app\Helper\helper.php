<?php

use App\Models\BlackFridayRequest;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Mail;
use App\Mail\OrderSendStaticMail;
function getPublicPathOnServer(){
    // just for check if this on local server or production server
    if($_SERVER['SERVER_NAME']=='baladi-atyaab.test'){
        return public_path();
    }

    return '/home/<USER>/public_html';
}
function resizeImagePost($image,$file_name,$width=null,$height=null)
{
    $image =getPublicPathOnServer().'/'.$image;

    $destinationPath = getPublicPathOnServer().'/momannew';

    $img = \Image::make($image);
    $img->resize($width, $height, function ($constraint) {
        $constraint->aspectRatio();
        $constraint->upsize();
    })->save($destinationPath.'/'.'23_'.$file_name);

    return $destinationPath.'/'.'23_'.$file_name;
}
function save_image64($image){
    $name = time().'.'.explode('/',explode(':',substr($image,0,strpos($image,';')))[1])[1];

    \Image::make($image)->save(getPublicPathOnServer().'/setting_image/'.$name);
    // dashbord/userImage/user.png
    //  $data=['success','name'=>$name,'path'=>asset('/setting_image/'.$name)];
    return $name;
}
function saveBase64Image($image, $direction,$width=null,$hight=null,$image_type=null,$water_mark=false)
{
    //dd(getimagesize($image));
    $image_data = getimagesize($image);
    $image_width = $image_data[0];
    $image_height = $image_data[1];

    $img = Image::make($image);

    $mime = explode('/', $img->mime)[1];

    // check direction
    $dir = 'uploads/'. $direction;
    //mkdir(my_public(). $dir);

    if(!File::exists(getPublicPathOnServer().'/uploads/'. $direction .'/')){
        File::makeDirectory(getPublicPathOnServer().'/uploads/'.$direction, 0755, true);
    }
    if(!File::exists(getPublicPathOnServer() .'/'. $dir)){
        File::makeDirectory(getPublicPathOnServer().'/'. $dir, 0755, true);
    }


    // check thump direction
    if(!File::exists(getPublicPathOnServer().'/uploads/'. $direction .'/')){
        File::makeDirectory(getPublicPathOnServer().'/uploads/'.$direction.'/thump', 0755, true);
    }

    if(!File::exists(getPublicPathOnServer() .'/'. $dir .'/thump/')){
        File::makeDirectory(getPublicPathOnServer(). '/'.$dir .'/thump/', 0755, true);
    }

    if(!File::exists(getPublicPathOnServer() .'/'. $dir .'/thump_770/')){
        File::makeDirectory(getPublicPathOnServer().'/'.  $dir .'/thump_770/', 0755, true);
    }

    if(!File::exists(getPublicPathOnServer() .'/'. $dir .'/thump_370/')) File::makeDirectory(getPublicPathOnServer().'/'.  $dir .'/thump_370/', 0755, true);
    if(!File::exists(getPublicPathOnServer() .'/'. $dir .'/thump_120/'))  File::makeDirectory(getPublicPathOnServer().'/'.  $dir .'/thump_120/', 0755, true);


    // save Image
    $file_name = rand(10000, 99999) . '.' . $mime;
    $img->save(getPublicPathOnServer() .'/'. $dir . '/' .$file_name);

    $image_big=getPublicPathOnServer() .'/'. $dir . '/' .$file_name;
    if($image_type=='small'){
        compress_image($image_big,$image_big, 100);
    }
    // save_thump
    if($width){

        $thump_image = $img->resize($width, $hight);
        $img->save(getPublicPathOnServer() .'/'. $dir . '/' .$file_name);
        $thump_image = $img->resize(370, 230);
        $img->save(getPublicPathOnServer() .'/'. $dir . '/thump_370/' .$file_name);
        $image_big=getPublicPathOnServer() .'/'. $dir . '/' .$file_name;
        $image_thumb=getPublicPathOnServer() .'/'. $dir . '/thump_370/' .$file_name;
        compress_image($image_big,$image_big, 100);
        compress_image($image_thumb,$image_thumb, 100);

    }
    else{
        if($image_width < 770 ||$image_height<480){
            //$watermark = Image::make(my_public().'/img/31993.png');
            //$watermark->insert($img, 'center');
            $img->save(getPublicPathOnServer() .'/'. $dir . '/thump_770/' .$file_name);

        }else{
            $img->resize(770, 480);
            $img->save(getPublicPathOnServer() .'/'. $dir . '/thump_770/' .$file_name);

        }

        $thump_image2 = $img->resize(370, 230);
        $img->save(getPublicPathOnServer() .'/'. $dir . '/thump_370/' .$file_name);
        $thump_image3 = $img->resize(170, 155);
        $img->save(getPublicPathOnServer() .'/'. $dir . '/thump_120/' .$file_name);

        $image_image1=getPublicPathOnServer() .'/'. $dir . '/thump_770/' .$file_name;
        $image_image2=getPublicPathOnServer() .'/'. $dir . '/thump_370/' .$file_name;
        $image_image3=getPublicPathOnServer() .'/'. $dir . '/thump_120/' .$file_name;
        $image_width = $image_data[0];
        compress_image($image,$image_image1, 100,770,480,$image_width,$image_height);
        compress_image($image,$image_image2, 100,370,230,$image_width,$image_height);
        compress_image($image,$image_image3, 100,170,155,$image_width,$image_height);
    }
    $data = ['name'=>$file_name,'path'=>$dir.'/'.$file_name];
    return $data;
}
function compress_image($source_url, $destination_url, $quality,$after_width,$after_height,$width,$height) {
    $info = getimagesize($source_url);
    if ($width > $after_width) {

        //get the reduced width
        $reduced_width = ($width - $after_width);
        //now convert the reduced width to a percentage and round it to 2 decimal places
        $reduced_radio = round(($reduced_width / $width) * 100, 2);

        //ALL GOOD! let's reduce the same percentage from the height and round it to 2 decimal places
        $reduced_height = round(($height / 100) * $reduced_radio, 2);
        //reduce the calculated height from the original height
        $after_height = $height - $reduced_height;
    }

    if ($info['mime'] == 'image/jpeg') {
        $image = imagecreatefromjpeg($source_url);
        imagejpeg($image, $destination_url, $quality);
    }
    elseif ($info['mime'] == 'image/gif'){
        $image = imagecreatefromgif($source_url);
        imagejpeg($image, $destination_url, $quality);
    }
    elseif ($info['mime'] == 'image/png'){
        $img = imagecreatefrompng($source_url);
        $targetLayer=imagecreatetruecolor($after_width,$after_height);
        imagealphablending($targetLayer, false);
        imagesavealpha($targetLayer,true);
        $transparency = imagecolorallocatealpha($targetLayer, 255, 255, 255, 127);
        imagefilledrectangle($targetLayer, 0, 0, $after_width, $after_height, $transparency);
        imagecopyresampled($targetLayer,$img,0,0,0,0,$after_width,$after_height, $width,$height);

        imagepng($targetLayer,$destination_url,9);
    }



    //save file


    //return destination file
    return $destination_url;
}
function saveFile($file, $direction)
{
    // dd($file,$direction);
    $mime = $file->getClientOriginalExtension();
    $allowed_extensions = ['jpeg',"JPEG", 'png','PNG','jpg',"JPG","gif","GIF",'jfif','webp']; // must be an array. Extensions disallowed to be uploaded
    $dir = '/uploads/'. $direction ;
    // dd($mime);
    if(in_array($mime,$allowed_extensions)){
        $file_name = rand(10000, 99999) . '.' . $mime;

        $data = ['name'=>$file_name,'path'=>$dir.'/'.$file_name];
    }
    else{
        $data = ['name'=>'','path'=>''];
    }


    return $data ;
}
function is_base64($s)
{
    return (bool) preg_match('/^[a-zA-Z0-9\/\r\n+]*={0,2}$/', $s);
}
function IsBase64($s)
{
    // Check if there are valid base64 characters
    if (!preg_match('/^[a-zA-Z0-9\/\r\n+]*={0,2}$/', $s)) return false;
    // Decode the string in strict mode and check the results
    $decoded = base64_decode($s, true);
    if(false === $decoded) return false;
    // if string returned contains not printable chars
    if (0 < preg_match('/((?![[:graph:]])(?!\s)(?!\p{L}))./', $decoded, $matched)) return false;
    // Encode the string again
    if(base64_encode($decoded) != $s) return false;
    return true;
}

function send_smsMobile($admin_numbers,$code , $user , $action= null){

    $messgmobile =   \Lang::get('lang.otp_msg') . " " . $code ;

    if( $user->phone_code =="970" ||  $user->phone_code =="972"){

        $admin_numbers =  getFullMobile($user->phone_code, $admin_numbers);

        $messgmobile_encoded = urlencode($messgmobile);
        //$admin_numbers ="**********";
            $url = 'https://smspal.net/api.php?comm=sendsms&user=WinToo&pass=258258&to=' . $admin_numbers . '&message=' . $messgmobile_encoded . '&sender=Win+Too';
//    $url ='https://www.tweetsms.ps/api.php?comm=sendsms&api_key=$2y$10$2SlLTd92ZJcRtgZALxBBqOs.dxtCPZ5DzFoVJqG8C8LyvBFx.i4JW&to=' . $admin_numbers . "&message=" . $messgmobile . "&sender=Merhaba.ps";
// $url = 'http://tweetsms.ps/api.php?comm=sendsms&api_key=$2y$10$g8.AmFP6EO.2Fx078SQ3d.LWlE2GbNn8Xh2Y0kJDoydaHbXIHXWAa&to=' . $admin_numbers . "&message=" . $messgmobile . "&sender=Zara.com.ps";


        $ch = curl_init();
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_URL, $url);
        $content = curl_exec($ch);


    }
    else
     if( $user->phone_code =="962"){
         
         
        $admin_numbers =  getFullMobile($user->phone_code, $admin_numbers);
        // $admin_numbers = str_replace('+', '',$admin_numbers);

        // $messgmobile_encoded = urlencode($messgmobile);
        // $messgmobile = "VerificationCode:".$Code;
        $messgmobile_encoded = urlencode($messgmobile);
        //$admin_numbers ="**********";
            // $url = 'https://smspal.net/api.php?comm=sendsms&user=WinToo&pass=258258&to=' . $admin_numbers . '&message=' . $messgmobile_encoded . '&sender=Win+Too';
            // $url = 'https://josmsservice.com/SMSServices/Clients/Prof/RestSingleSMS_General/SendSMS?senderid=WinApp&amp;numbers=' . $admin_numbers . '&amp;accname=winapp&amp;AccPass=eG9gT7tL6wA6jM8p&amp;msg=' . $messgmobile_encoded . '';

        // $url = "https://josmsservice.com/SMSServices/Clients/Prof/RestSingleSMS/SendSMS?senderid=WinApp&numbers=' . $admin_numbers . '&accname=winapp&AccPass=eG9gT7tL6wA6jM8p&msg=' . $messgmobile . ''";
        $url = "https://josmsservice.com/SMSServices/Clients/Prof/RestSingleSMS/SendSMS?senderid=WinApp&numbers=".$admin_numbers."&accname=winapp&AccPass=eG9gT7tL6wA6jM8p&msg=".$messgmobile_encoded;
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_URL, $url);
        $content = curl_exec($ch);
         
     }
    else{
        send_smsMobileTwilio(getFullMobile($user->phone_code,$admin_numbers) ,$messgmobile );
    }


}
function send_smsMobileTwilio($admin_numbers,$admin_msg){

    //$admin_numbers ="**********";
//    $url ='https://www.tweetsms.ps/api.php?comm=sendsms&api_key=$2y$10$2SlLTd92ZJcRtgZALxBBqOs.dxtCPZ5DzFoVJqG8C8LyvBFx.i4JW&to=' . $admin_numbers . "&message=" . $messgmobile . "&sender=Merhaba.ps";
// $url = 'http://tweetsms.ps/api.php?comm=sendsms&api_key=$2y$10$g8.AmFP6EO.2Fx078SQ3d.LWlE2GbNn8Xh2Y0kJDoydaHbXIHXWAa&to=' . $admin_numbers . "&message=" . $messgmobile . "&sender=Zara.com.ps";
    $accountSid = "**********************************";
    $authToken = "cbaa7d12bf4ca1421f1bf3fce8fecc33";
    $url = "https://api.twilio.com/2010-04-01/Accounts/**********************************/Messages.json";
    $auth = base64_encode("{$accountSid}:{$authToken}");

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);

     curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
            "To"=>"+".$admin_numbers ,
            "From"=>"+***********" ,
            "Body" => $admin_msg  ]));

        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Authorization: Basic '.$auth,
            'Content-Type:application/x-www-form-urlencoded',
        ));
    $content = curl_exec($ch);
    file_put_contents('twilio.log',print_r($content,true).'\n',FILE_APPEND);

}


function twilio_send_otp($mobile){

    $accountSid = "**********************************";
    $authToken = "cbaa7d12bf4ca1421f1bf3fce8fecc33";
    $verifySid = "VAbbd649238e16d45ce07103114baf6265";
    $url = "https://verify.twilio.com/v2/Services/{$verifySid}/Verifications";
    $auth = base64_encode("{$accountSid}:{$authToken}");
;    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query(["To"=>("+".$mobile) , "Channel" => "sms"  ]));
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type:application/x-www-form-urlencoded',
        'Accept:application/json'
    ));

    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Authorization: Basic '.$auth,
    ));

    $content = curl_exec($ch);

    /*if($content){

        $res  = json_decode($content);

    }*/
    file_put_contents('twilio.log',print_r($content,true).'\n',FILE_APPEND);

}

function twilio_verify_otp($mobile,$code){

    $accountSid = "**********************************";
    $authToken = "cbaa7d12bf4ca1421f1bf3fce8fecc33";
    $verifySid = "VAbbd649238e16d45ce07103114baf6265";
    $url = "https://verify.twilio.com/v2/Services/{$verifySid}/VerificationCheck";
    $auth = base64_encode("{$accountSid}:{$authToken}");
;    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query(["To"=>("+".$mobile) , "Code" => $code  ]));
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type:application/x-www-form-urlencoded',
        'Accept:application/json'
    ));

    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Authorization: Basic '.$auth,
    ));

    $content = curl_exec($ch);

    file_put_contents('twilio_verify.log',print_r($content,true).'\n',FILE_APPEND);
    if ($content){
        return $content->valid ;
    }

    return false ;


}



function priceWithAndWithoutTax($price,$qty,$tax){

    $percNumber = $tax / 100.00;
    $total = $qty *$price;
    $tax_amount = $total -($total/($percNumber+1));
    $tax_amount = ($tax_amount);
//    dd($tax_amount,$total,$percNumber,$total-$tax_amount);
    return ['tax_amount'=>myRound($tax_amount),'price_without_tax'=>myRound($total-$tax_amount) ,'price_with_tax'=>myRound($total), 'tax'=>$tax , 'product_qty'=>$qty ];

}

function myRound($price , $precision = 2){
    return round($price,$precision);
}

function getValueForAttributeOnAtrributeTemplateBladePage($attribute_id,$array){
    foreach ($array as $item){
        if($item['key']==$attribute_id){
            return $item['value'];
        }
    }
    return false;
}

function PriceReturn($price,$currency){
    $res = false;
    if ($price == (int)$price){//int
        return $currency.$price.',-';
    }else{//float
        return $currency.round($price,2);
    }

}
function ArabicDate($date) {
    $months = array("Jan" => "يناير", "Feb" => "فبراير", "Mar" => "مارس", "Apr" => "أبريل", "May" => "مايو", "Jun" => "يونيو", "Jul" => "يوليو", "Aug" => "أغسطس", "Sep" => "سبتمبر", "Oct" => "أكتوبر", "Nov" => "نوفمبر", "Dec" => "ديسمبر");
    $your_date = $date; // The Current Date
    $en_month = date("M", strtotime($your_date));
    foreach ($months as $en => $ar) {
        if ($en == $en_month) { $ar_month = $ar; }
    }

    $find = array ("Sat", "Sun", "Mon", "Tue", "Wed" , "Thu", "Fri");
    $replace = array ("السبت", "الأحد", "الإثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة");
    $ar_day_format = date("D", strtotime($your_date)); // The Current Day
    $ar_day = str_replace($find, $replace, $ar_day_format);

    header('Content-Type: text/html; charset=utf-8');
    $standard = array("0","1","2","3","4","5","6","7","8","9");
    $eastern_arabic_symbols = array("0","1","2","3","4","5","6","7","8","9");
    $current_date = ' '.date("d", strtotime($your_date)).' , '.$ar_month.'  '.date("Y", strtotime($your_date));
    $arabic_date = str_replace($standard , $eastern_arabic_symbols , $current_date);

    return $arabic_date;
}

function getTimeFormate($date,$type){
    $dtFormat = "Y-m-d H:i:s"; //MySQL Datetime format
    $curDT = $date;
    $curTime = strtotime($curDT);
    if($type == 'time'){
        $nowFormat = "h:i ";
        $arrEn = array('am', 'pm');
        $arrAr = array('ص', 'م');
        return  date($nowFormat).str_replace($arrEn, $arrAr,date("a", $curTime));
    }else{
        $nowFormat = "Y-m-d";
        return  $date;
    }

}

function generateNDigitRandomNumber($length){
   // return "111111"  ;
    return mt_rand(pow(10,($length-1)),pow(10,$length)-1);
}

function checkStoreProductCount($get_product_number=false){
    $store = auth('store')->user();
    $subscription =$store->subscription->last();
    $plan = $subscription->plan;

    $plan_product_count = $plan->product;
    $store_product_count =$store->product_count;
    if($get_product_number){
        return $plan_product_count-$store_product_count;
    }
    $expired_date =$subscription->expired_date;
    $current_date = \Carbon\Carbon::now();
    if($store_product_count < $plan_product_count  || $current_date < $expired_date ){
        return true;
    }else{
        return false;
    }

}

function checkStoreAuthorized($product_store_id){
    if($product_store_id != auth('store')->id()){
        abort(404);
        exit();
    }
}
function priceWithTax($price,$tax){

    $percNumber = $tax / 100.00;
    $total = $price;
    $tax_amount = $total -($total/($percNumber+1));
    $tax_amount = ($tax_amount);

    return myRound($total+$tax_amount);

}


function search_by_value($array, $key, $value,$key2,$value2)
{
    $results = array();

    if (is_array($array)) {
        if ((isset($array[$key]) && $array[$key] == $value)&&(isset($array[$key2]) && $array[$key2] == $value2)) {
            $results[] = $array;
        }

        foreach ($array as $subarray) {
            $results = array_merge($results, search_by_value($subarray, $key, $value,$key2,$value2));
        }
    }

    return $results;
}



function searchAndReturnIndex($array, $key, $value)
{
    $results = array();

    if (is_array($array)) {
        if (isset($array[$key]) && $array[$key] == $value) {
            $results[] = $array;
        }

        foreach ($array as $subarray) {
            $results = array_merge($results, search($subarray, $key, $value));
        }
    }

    return $results;
}

function fcmNotification($user, $sendData , $withNotification = false){


    //get function from other controller

    $data= new \App\Services\PushNotification();


    if($user->fcm_token){
        try {
            $data->setUpNewSendMessage($user,$sendData , $withNotification);
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }

}



function notify($notification){


    $data = $notification['data'];


    $link =$data['actionURL'];

    if($data['type'] =='subscribe'){
        $icon = 'feather icon-message-square font-medium-5 success';
        $title = 'طلب اشتراك !';
        $color='success';
    }
    if($data['type'] =='general_notification'){
        $icon = 'feather icon-message-square font-medium-5 success';
        $title = 'اشعار عام !';
        $color='warning';
    }
    if($data['type'] =='order'){
        $icon = 'feather icon-plus-square font-medium-5 success';
        $title = 'تم طلب طلبية جديدة !';
        $color='success';
    }
    $text = $data['body'];
    $date = $notification['created_at']->diffForHumans();

    $html='<a class="d-flex justify-content-between" >
              <div class="media d-flex align-items-start"> <div class="media d-flex align-items-start">
                <div class="media-left"><i class="'.$icon.'"></i></div>
                <div class="media-body">
                <h6 class="'.$color.' media-heading red darken-1">'.$title.'</h6><small class="notification-text">'.$text.'</small>
                </div>
                <time class="media-meta" datetime="2015-06-11T18:29:20+08:00">'.$date.'</time></small>
                </div>
                </a>';


    return $html;

}

function numberFormat($number, $decimals = 0, $decPoint = '.' , $thousandsSep = ',')
{
    $negation = ($number < 0) ? (-1) : 1;
    $coefficient = pow(10, $decimals);
    $number = $negation * floor((string)(abs($number) * $coefficient)) / $coefficient;
    $number=number_format($number, $decimals, $decPoint, $thousandsSep);
    $final = (double)$number;
    return round($final,2);
}

function cutNum($num, $precision = 2) {
    $integerPart = floor($num);
    $decimalPart = str_replace($integerPart, '', $num);
    $trimmedDecimal = substr($decimalPart, 0, $precision + 1);
    return $integerPart . $trimmedDecimal;
}

if(!function_exists('rgb2lab'))
{
    function rgb2lab($rgb) {
        $eps = 216/24389; $k = 24389/27;
        // reference white D50
        $xr = 0.964221; $yr = 1.0; $zr = 0.825211;
        // reference white D65
        #$xr = 0.95047; $yr = 1.0; $zr = 1.08883;

        // RGB to XYZ
        $rgb[0] = $rgb[0]/255; //R 0..1
        $rgb[1] = $rgb[1]/255; //G 0..1
        $rgb[2] = $rgb[2]/255; //B 0..1

        // assuming sRGB (D65)
        $rgb[0] = ($rgb[0] <= 0.04045)?($rgb[0]/12.92):pow(($rgb[0]+0.055)/1.055,2.4);
        $rgb[1] = ($rgb[1] <= 0.04045)?($rgb[1]/12.92):pow(($rgb[1]+0.055)/1.055,2.4);
        $rgb[2] = ($rgb[2] <= 0.04045)?($rgb[2]/12.92):pow(($rgb[2]+0.055)/1.055,2.4);

        // sRGB D50
        $x =  0.4360747*$rgb[0] + 0.3850649*$rgb[1] + 0.1430804*$rgb[2];
        $y =  0.2225045*$rgb[0] + 0.7168786*$rgb[1] + 0.0606169*$rgb[2];
        $z =  0.0139322*$rgb[0] + 0.0971045*$rgb[1] + 0.7141733*$rgb[2];
        // sRGB D65
        /*$x =  0.412453*$rgb[0] + 0.357580*$rgb[1] + 0.180423*$rgb[2];
        $y =  0.212671*$rgb[0] + 0.715160*$rgb[1] + 0.072169*$rgb[2];
        $z =  0.019334*$rgb[0] + 0.119193*$rgb[1] + 0.950227*$rgb[2];*/

        // XYZ to Lab
        $xr = $x/$xr; $yr = $y/$yr; $zr = $z/$zr;

        $fx = ($xr > $eps)?pow($xr, 1/3):($fx = ($k * $xr + 16) / 116); $fy = ($yr > $eps)?pow($yr, 1/3):($fy = ($k * $yr + 16) / 116); $fz = ($zr > $eps)?pow($zr, 1/3):($fz = ($k * $zr + 16) / 116);

        $lab = array();
        $lab[] = round(( 116 * $fy ) - 16); $lab[] = round(500*($fx-$fy)); $lab[] = round(200*($fy-$fz));
        return $lab;
    } // function rgb2lab
}

if(!function_exists('deltaE'))
{
    function deltaE($lab1, $lab2)
    {
        // CMC 1:1
        $l = 1; $c = 1;

        $c1 = sqrt($lab1[1]*$lab1[1]+$lab1[2]*$lab1[2]); $c2 = sqrt($lab2[1]*$lab2[1]+$lab2[2]*$lab2[2]);

        $h1 = (((180000000/M_PI) * atan2($lab1[1],$lab1[2]) + 360000000) % 360000000)/1000000;

        $t = (164 <= $h1 AND $h1 <= 345)?(0.56 + abs(0.2 * cos($h1+168))):(0.36 + abs(0.4 * cos($h1+35)));
        $f = sqrt(pow($c1,4)/(pow($c1,4) + 1900));

        $sl = ($lab1[0] < 16)?(0.511):((0.040975*$lab1[0])/(1 + 0.01765*$lab1[0]));
        $sc = (0.0638 * $c1)/(1 + 0.0131 * $c1) + 0.638;
        $sh = $sc * ($f * $t + 1 -$f);

        return sqrt( pow(($lab1[0]-$lab2[0])/($l * $sl),2) + pow(($c1-$c2)/($c * $sc),2) + pow(sqrt(($lab1[1]-$lab2[1])*($lab1[1]-$lab2[1]) + ($lab1[2]-$lab2[2])*($lab1[2]-$lab2[2]) + ($c1-$c2)*($c1-$c2))/$sh,2) );
    } // function deltaE
}

if(!function_exists('colorDistance'))
{
    function colorDistance($lab1,$lab2)
    {
        return sqrt(($lab1[0]-$lab2[0])*($lab1[0]-$lab2[0])+($lab1[1]-$lab2[1])*($lab1[1]-$lab2[1])+($lab1[2]-$lab2[2])*($lab1[2]-$lab2[2]));
    }
}

if(!function_exists('str2rgb'))
{
    function str2rgb($str)
    {
        $str = preg_replace('~[^0-9a-f]~','',$str);
        $rgb = str_split($str,2);
        for($i=0;$i<3;$i++)
            $rgb[$i] = intval($rgb[$i],16);

        return $rgb;
    } // function str2rgb
}

function getNearestColor($givenColor,$palette=
 array('silver'=>'C0C0C0',
     'gray'=>'808080',
     'white'=>'FFFFFF',
     'maroon'=>'800000',
     'red'=>'FF0000',
     'purple'=>'800080',
     'fuchsia'=>'FF00FF',
     'green'=>'008000',
     'lime'=>'00FF00',
     'olive'=>'808000','yellow'=>'FFFF00','navy'=>'000080',
     'blue'=>'0000FF',
     'deepblue' => '1e15f6',
     "greensky"=>'51EF34',
     "skyblue"=>'3895D3',
     "beige"=>'c29978',
     'teal'=>'008080','deeppink'=>'ff1493','darkviolet'=>'9400d3','cornflowerblue'=>'6495ed','deepskyblue'=>'00bfff',
     'orange'=>'FFA500','black'=>'000000',
 )
){




    // split into RGB, if not already done
    $givenColor =strtolower($givenColor);

    $givenColorRGB = is_array($givenColor)?$givenColor:str2rgb($givenColor);
    $min = 0xffff;
    $return = NULL;

    foreach($palette as $key => $color)
    {
        // split into RGB

        $color = strtolower($color);
        $color = is_array($color)?$color:str2rgb($color);
        // deltaE
        #if($min >= ($deltaE = deltaE(rgb2lab($color),rgb2lab($givenColorRGB))))
        // euclidean distance
        if($min >= ($deltaE = colorDistance(rgb2lab($color),rgb2lab($givenColorRGB))))
        {
            $min = $deltaE;
            $return = $key;
        }
    }

    return $return;
}
function getFinalPrice($type,$profit_amount,$price,$offer_discount_type,$percentage_value){

    $profit_price=0;
    $new_price=0;
    $discount_amount=0;

    if($type === 'fixed'){
        $profit_price = $price+$profit_amount;
    }

    if($type === 'percentage'){

        $profit_amount=($price * ($profit_amount / 100));
        $profit_price=$price+($price * ($profit_amount / 100));
    }


    if($offer_discount_type === 'fixed'){
        $new_price= $profit_price-$percentage_value;
        $discount_amount=$percentage_value;
    }
    if($offer_discount_type === 'percentage'){

        $discount_amount=($profit_price * ($percentage_value / 100));
        $new_price=$profit_price-($profit_price * ($percentage_value / 100));
    }

    return ['profit_price'=>$profit_price,'discount_amount'=>$discount_amount,'new_price'=>$new_price];
}

function getRound($number){
    return round($number);
}
function checkVariationQuantityForProduct($id){

    $product = \App\Models\Product::find($id);
    $variations_sum  = $product->variations->sum('stock_quantity');
    if($variations_sum == 0){
        $product->status = 0;
        $product->is_available = 0;
        $product->save();
    }else{

        $product->status = 1;

        $product->is_available = 1;

        $product->save();
    }

}

function fixPriceWithStorjanUpdatedProduct($id){
    $product = \App\Models\Product::find($id);

    if($product->profit_type === 'fixed'){
        $product->profit_price = $product->price+$product->profit_amount;
        $product->profit_amount=$product->profit_amount;

    }
    if($product->profit_type === 'percentage'){

        $profit_amount =($product * ($product->product->profit_amount / 100));
        $product->profit_price=$product->price+($product->price * ($product->profit_amount / 100));
    }

    if($product->offer_discount_type === 'fixed'){
//                $this->product->new_price= $this->product->price-$this->product->percentage_value;
//                $this->product->discount_amount=$this->product->percentage_value;
        $product->new_price= $product->profit_price-$product->percentage_value;
        $product->discount_amount=$product->percentage_value;

    }
    if($product->offer_discount_type === 'percentage'){
        $product->discount_amount=($product->profit_price * ($product->percentage_value / 100));
        $product->new_price=$product->profit_price-($product->profit_price * ($product->percentage_value / 100));
    }

    $product->save();

}
function fixPriceWithForVariation($id){
    $variation = \App\Models\Variation::find($id);
    $product = \App\Models\Product::find($variation->product_id);

    if($product->profit_type === 'fixed'){
        $profit_amount=$product->profit_amount;
        $profit_price=$variation['display_selling_price']+$product->profit_amount;
        $profit_type='fixed';
    }
    if($product->profit_type === 'percentage'){
        $profit_amount=($variation['display_selling_price'] * ($product->profit_amount / 100));
        $profit_price=$variation['display_selling_price']+($variation['display_selling_price']* ($product->profit_amount / 100));
        $profit_type='percentage';
    }

    if($product->offer_discount_type === 'fixed'){

        $variation_price_after_offer= $profit_price- $product->percentage_value;
        $variation_offer_type='fixed';
        $offer_price=$product->percentage_value;
    }
    if($product->offer_discount_type === 'percentage'){

        $variation_price_after_offer=$profit_price-($profit_price * ($product->percentage_value / 100));
        $variation_offer_type='percentage';
        $offer_price=$profit_price * ($product->percentage_value / 100);
    }

    $variation->update([
        'offer_type'=>$variation_offer_type,
        'price_after_offer'=>$variation_price_after_offer,
        'offer_price'=>$offer_price,
        'profit_type'=>$profit_type,
        'profit_price'=>$profit_price,
        'profit_amount'=>$profit_amount]);
}


function getColorName($id){

    return  optional(\App\Models\Maincolor::find($id))->human_name??'غير محدد';

}
function getSizeName($id){

    return  optional(\App\Models\Size::find($id))->size_ar??'غير محدد';

}

function getProductPrice($product,$variation){
    /*if($product->new_price){
        $price = getRound(number_format((float)$product->price , 2, '.', ''));
        $new_price = getRound(number_format((float)$variation->price_after_offer ,
            2,
            '.', ''));
    }else{
        $price = $product->price  ;
        $new_price = $product->price ;
    }*/

    if ($variation){
        if($variation->price_after_offer){
            $price = round($variation->price_after_offer);
        }else{
            $price= round($variation->display_selling_price) ;
        }

    }else{

        if($product->is_offer){
            $price = round($product->new_price) ;
        }else{
            $price = round($product->price) ;

        }


    }

    return $price;
}

function checkIfUserStore(){
    $user = auth()->user();
    if ($user->type == "STORE"){
        return true;
    }
    return false;
}
function getCurrentStoreUser(){
    $user = auth()->user();
    if ($user->type == "STORE"){
        return \App\Models\Store::find($user->store_id);
    }
    return $user;
}

function getCurrentUserAvatar(){
    $user = auth()->user();
    if ($user->type == "STORE"){
        return \App\Models\Store::find($user->store_id)->logo_url;
    }
    return $user->avatarUrl();
}


function getCurrentMenu(){
    $user = auth()->user();
    if ($user->type == "STORE"){
        return "store_menu";
    }
    return "admin_menu";
}
function getRateBaseOnProductType($product_type){

    if($product_type == "store_jan_product"){
        $rate = \App\Models\ExchangRate::where("type","storejan")->latest()->first()->rate;
        return $rate;
    }

    $rate = \App\Models\ExchangRate::where("type","manual_product")->latest()->first()->rate;
    return $rate;
}


function addBlackFridaySubscription ($store , $price){
    $startDate=\Carbon\Carbon::now()->format('Y-m-d H:i:s');
    $endtDate=\Carbon\Carbon::now()->addMonths(6)->format('Y-m-d H:i:s');

    $blackFriday  = BlackFridayRequest::create([
        "store_id"=>$store->id,
        "status"=>$price == 0,
        "price"=>$price,
        "note"=>"HotSale",
        "start_at"=>$startDate,
        "end_at"=>$endtDate,
    ]);

return $blackFriday ;

}


function getFullMobile($country_code , $mobile){

    $mobileNumberWithoutLeadingZero = preg_replace('/^0/', '', $mobile, 1);

    return $country_code.(startsWith($mobile,"0")?$mobileNumberWithoutLeadingZero:$mobile);

 }


function getCurrentUserCurrency(){

    $currentUser = auth('customers')->user();
    $currentStore = auth('store')->user();

    if ($currentUser)return $currentUser->currency ;
    elseif ($currentStore)return $currentStore->currency ;
    else return \App\Models\Currency::usd();

}
function getCurrentUserCurrencyUSD(){

  return \App\Models\Currency::usd();

}
function getPriceForUser($amount , $currency){

    $user_currency = getCurrentUserCurrency();

    return currency_convert($amount , $currency , $user_currency) ;



}

function currency_convert($amount , $currency , $to_currency){

    if (is_null($currency) || $currency->usd_exchange_rate === 0)
    return is_nan($amount)||is_infinite($amount)?dd($amount,$currency) :$amount;

    // Convert the amount to USD using the from currency rate
    $usdAmount = $amount / $currency->usd_exchange_rate;

    // Convert the USD amount to the target currency using the to currency rate
    $targetAmount = $usdAmount * $to_currency->usd_exchange_rate;

    return is_nan($targetAmount)||is_infinite($targetAmount)?dd($targetAmount,$currency) :$targetAmount;

}

function startsWith ($string, $startString)
{
    $len = strlen($startString);
    return (substr($string, 0, $len) === $startString);
}


// Function to check the string is ends
// with given substring or not
function endsWith($string, $endString)
{
    $len = strlen($endString);
    if ($len == 0) {
        return true;
    }
    return (substr($string, -$len) === $endString);
}

  function execute($cmd): string
{
    $process = \Symfony\Component\Process\Process::fromShellCommandline($cmd);

    $processOutput = '';

    $captureOutput = function ($type, $line) use (&$processOutput) {
        $processOutput .= $line;
    };

    $process->setTimeout(null)
        ->run($captureOutput);

    if ($process->getExitCode()) {
        $exception = new Exception($cmd . " - " . $processOutput);
        report($exception);

        throw $exception;
    }

    return $processOutput;
}


function toUTCDate($local_date , $format = 'Y-m-d H:i:s'){

    //$local_date = "2023-05-11 22:00:00" ;
// create a DateTime object for the original date
    // create a DateTime object for the local date
    $local_offset = request()->header('X-Client-Timezone-Offset');

    $local_timezone = getTimeZoneFromOffset($local_offset);

    $date = DateTime::createFromFormat('Y-m-d H:i:s', $local_date, new DateTimeZone($local_timezone));

// create a DateTimeZone object for UTC timezone
    $timezone = new DateTimeZone('UTC');

// set the timezone for the DateTime object to UTC
    $date->setTimezone($timezone);


// format the new date in UTC timezone
    return $date->format($format);
}

function getTimeZoneFromOffset( $local_timezone)
{

        $sign = startsWith($local_timezone , "-")?"-":"+" ;

        $local_timezone =    str_replace("-","",str_replace("+","",$local_timezone));

        return $sign.date("H:i" , strtotime($local_timezone));

}

function toLocalDate($utc_date , $format = 'Y-m-d H:i:s' ){

    if (is_null($utc_date)) return $utc_date ;

    //$local_date = "2023-05-11 22:00:00" ;
// create a DateTime object for the original date
    // create a DateTime object for the local date
    // create a DateTime object for the UTC date

    $utc_date = DateTime::createFromFormat($format, $utc_date, new DateTimeZone('UTC'));

    $local_offset = request()->header('X-Client-Timezone-Offset');

    $local_timezone = getTimeZoneFromOffset($local_offset);


// create a DateTimeZone object for the local timezone offset
    $local_timezone = new DateTimeZone($local_timezone);

// set the timezone for the UTC DateTime object

     $utc_date->setTimezone($local_timezone);


// format the local date
    $local_date = $utc_date->format($format);
// format the new date in UTC timezone
    return $local_date ;
}

function getSupportedLocales(){

    return config("app.supported_locales");

}


function  formatInAllLocales($lang_key,...$values):array {
    $translations = [] ;


    foreach (getSupportedLocales() as $locale){
        $bindings = [] ;
        foreach ($values as $value){
            if (is_array($value)){
            $bindings[] = isset($value[$locale])?$value[$locale]:json_encode($value);

            }else
             $bindings[] = $value;

        }

        $translations[$locale] = sprintf(Lang::get($lang_key,[],$locale),...$bindings);

    }

    return ($translations)  ;

}


function setupElasticSearchForArabic($indexName){


    $client = \Elastic\Elasticsearch\ClientBuilder::create()
        ->setHosts(["127.0.0.1:9200"])
        ->build();


    $settings = [
        'settings' => [
            'index' => [
                'analysis' => [
                    'char_filter' => [
                        'remove_diacritics' => [
                            'type' => 'mapping',
                            'mappings' => [
                                '\u065B' => '', '\u065C' => '', '\u065D' => '', '\u064E' => '', '\u064F' => '', '\u0650' => '', '\u0651' => '', '\u0652' => ''
                            ],
                        ],
                    ],
                    'filter' => [
                        'arabic_stemmer' => [
                            'type' => 'stemmer',
                            'language' => 'arabic',
                        ],
                    ],
                    'analyzer' => [
                        'custom_arabic_analyzer' => [
                            'type' => 'custom',
                            'tokenizer' => 'standard',
                            'char_filter' => ['remove_diacritics'],
                            'filter' => ['lowercase', 'asciifolding', 'arabic_stemmer'],
                        ],
                    ],
                ],
            ],
        ],
        'mappings' => [
            'dynamic_templates' => [
                [
                    'strings' => [
                        'match_mapping_type' => 'string',
                        'mapping' => [
                            'type' => 'text',
                            'analyzer' => 'custom_arabic_analyzer',
                            'search_analyzer' => 'standard',
                        ],
                    ],
                ],
            ],
        ]
    ];
// Create the Elasticsearch index with the specified settings
    $params = [
        'index' => $indexName,
        'body' => $settings,
    ];


    if ($client->indices()->exists(['index' => $indexName])) {
        // Index exists, proceed with closing and updating

        try {
            $client->indices()->close(['index' => $indexName]);
        } catch (Exception $e) {
            echo "Error closing index: " . $e->getMessage();
            exit;
        }

// Update the index settings
        $params = [
            'index' => $indexName,
            'body' => $settings,
        ];

        try {
            $client->indices()->putSettings($params);
            echo "Index settings updated successfully.";
        } catch (Exception $e) {
            echo "Error updating index settings: " . $e->getMessage();
        }

// Reopen the index
        try {
            $client->indices()->open(['index' => $indexName]);
        } catch (Exception $e) {
            echo "Error reopening index: " . $e->getMessage();
        }

    } else {

        $response = $client->indices()->create($params);
        echo "Index '$indexName' created successfully.";

    }

}


function searchUsingElasticSearch($indexName ,$fieldName ,$query)
{
    // Define Elasticsearch client configuration
    $client = \Elastic\Elasticsearch\ClientBuilder::create()
        ->setHosts(['http://localhost:9200']) // Adjust the Elasticsearch server URL
        ->build();



// Define the search query
    $searchQuery = [
        'index' => $indexName,
        'body' => [
            'query' => [
                'bool' => [
                    'should' => [
                        [
                            'wildcard' => [
                                $fieldName => $query.'*',
                            ],
                        ],
                        [
                            'match' => [
                                $fieldName => [
                                    'query' => $query,
                                    'fuzziness' => 'AUTO',
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ],
    ];

    // Define the search query
    $searchQuery = [
        'index' => $indexName,
        'body' => [
            'query' => [
                'fuzzy' => [
                    $fieldName => [
                        'value' => $query,
                        'fuzziness' => 'AUTO', // Adjust fuzziness level as needed
                    ],
                ],
            ],
        ],
    ];
// Execute the search query
    try {
        $response = $client->search($searchQuery);

        // Process and display search results
        $hits = $response['hits']['hits'];
        foreach ($hits as $hit) {
            $result = $hit['_source'][$fieldName];
            echo "Matched: $result\n";
        }
    } catch (Exception $e) {
        echo "Error executing search query: " . $e->getMessage();
    }

    echo "Done" ;
}



