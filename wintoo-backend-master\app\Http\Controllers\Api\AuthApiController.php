<?php

namespace App\Http\Controllers\Api;

use App\Http\Resources\CustomerResource;
use App\Models\Store;
use App\Services\CometChat;
use Illuminate\Http\Request;
use App\Http\Controllers\BaseControllerApi;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Lang;
use Validator;
use App\Http\Controllers\Api\SocialLoginController;
use App\Models\Customer;

class AuthApiController extends BaseControllerApi
{

public function login(Request $request)
{      
    $this->validate($request, [
        'password' => 'required',
        'phone' => 'nullable', // Phone is now nullable
        'email' => 'nullable|email', // Email can be used as well
        'phone_code' => 'nullable',
        'access_token' => 'nullable'
    ]);

    $user = null;
    if ($request->has('access_token')) {
        $controller = new SocialLoginController();
        return $controller->authenticate('');
    }
    // Check if the user is logging in with an email
    if ($request->has('email')) {
        $user = Customer::where("email", $request->email)->first();
    }
    // If not, try with the phone number
    else if ($request->has('phone')) {
        $user = Customer::where("full_mobile", getFullMobile($request->phone_code, $request->phone))->first();
    }

    // Check if the user exists and the password is correct
    if (!$user || !\Hash::check($request->password, $user->password)) {
        return $this->sendError(\Lang::get("lang.wrong_credentials"), 422);
    }

    // Check if SMS verification is needed
    if ($user->sms_verify == false || $user->sms_verify == 0) {
        $token = \Str::random(64);

        if ($user->phone_code == "90") { // TR
            $sms_code = "111111";
        } else {
            $sms_code = generateNDigitRandomNumber(6);
        }

        $user->sms_code = $sms_code;
        $user->token = $token;
        $user->save();
        

        $msg = $user->sms_code;
        \Mail::to($user->email)->send(new \App\Mail\VerifyEmail($user->sms_code));

        $data = [
            'sms_verify' => false,
            'sms_code' => $user->sms_code,
            "token" => $token
        ];
        return $this->sendResponse($data, \Lang::get("lang.otp_sent"));
    }

    // Check if the account is blocked
    if ($user->status == false) {
        return $this->sendResponse(null, \Lang::get("lang.account_blocked"));
    }

    // Generate a token for the user
    $token = $user->createToken($request->header('X-Client-Device-Name'))->plainTextToken;

    // Register the user in CometChat if not already registered
    if (!$user->is_chat_register) {
        $chat = new CometChat();
        $chat->create_user($user, false, "user_" . $user->id);
        $user->save();
    }

    // Prepare the response
    $result = [
        'user' => new CustomerResource($user),
        'token' => $token,
    ];

    return $this->sendResponse($result, '');
}




 public function sendForgetPassword(Request $request)
{
    // Validate that the email field is required and properly formatted
    $this->validate($request, [
        'email' => 'required|email',
    ]);

    // Find the user by email
    $user = Customer::where('email', $request->email)->first();

    if (is_null($user)) {
        return $this->sendError(\Lang::get("lang.not_registered"), 422);
    }

    // Reset the sms_verify status
    $user->sms_verify = false;

    // Generate the verification code
    $sms_code = $this->generateNDigitRandomNumber(6);

    // Save the code and token to the user record
    $user->sms_code = $sms_code;
    $user->token = \Str::random(32);
    $user->save();

    $result = [
        'sms_verify' => false,
        'sms_code' => $user->sms_code,
        "token" => $user->token,
    ];

    // Send the verification code via email
    \Mail::to($user->email)->send(new \App\Mail\VerifyEmail($user->sms_code));

    return $this->sendResponse($result, \Lang::get("lang.otp_sent"));
}



    public function logout(Request $request){
        // $this->CURRENT_USER->fcm_token = null;
        // $this->CURRENT_USER->save();
        // $request->user()->currentAccessToken()->delete();
        //auth()->logout();
        return $this->sendResponse(null,\Lang::get("lang.logged_out"));
    }

    public  function generateNDigitRandomNumber($length){
        return random_int(100000, 999999).'';
        //   return mt_rand(pow(10,($length-1)),pow(10,$length)-1);
    }


public function register(Request $request)
{
    $this->validate($request, [
        'email' => 'required|email|unique:customers,email',
        'phone' => 'nullable|regex:/^[0-9]{9,10}$/|unique:customers,phone',
        'phone_code' => 'nullable',
        'username' => 'required',
        'password' => 'required'
    ]);

    $email_verification_code = \Str::random(6); // Generate a 6-digit verification code

    $full_mobile = null;
    if ($request->phone && $request->phone_code) {
        $full_mobile = getFullMobile($request->phone_code, $request->phone);
    }

    // Ensure that phone is not null. If it is, you can set it to an empty string or handle it differently
    $phone = $request->phone ?: '';

    // Create the customer without the token
    $customer = Customer::create([
        'email' => $request->email,
        'phone' => $phone,
        'full_mobile' => $full_mobile,
        'phone_code' => $request->phone_code,
        'username' => $request->username,
        'password' => Hash::make($request->password),
        'sms_code' => $email_verification_code,
        'sms_verify' => false,
        'is_chat_register' => false,
    ]);

    // Generate the token and update the customer record
    $token = $customer->createToken($request->header('X-Client-Device-Name'))->plainTextToken;
    $customer->token = $token;
    $customer->save();

    // Send verification email
    \Mail::to($customer->email)->send(new \App\Mail\VerifyEmail($customer->sms_code));

    $result = [
        'email_verified' => false,
        'email_verification_code' => $customer->sms_code,
        'token' => $token,
    ];

    if (!$customer->is_chat_register) {
        $chat = new CometChat();
        $chat->create_user($customer, false, "user_" . $customer->id);
         $customer->save();
    }

    return $this->sendResponse($result, \Lang::get("lang.registered_successfully"));
}




// public function verifyEmail(Request $request)
// {
//     $this->validate($request, [
//         'code' => 'required',
//         'token' => 'required',
//     ]);

//     $customer = Customer::where('sms_code', $request->code)
//         ->where('token', $request->token)
//         ->where('sms_verify', false)
//         ->first();

//     if (is_null($customer)) {
//         return $this->sendError(\Lang::get("lang.invalid_verification_code"), 422);
//     }

//     // Mark email as verified
//     $customer->sms_verify = true;  // Assuming this also signifies email verification
//      $customer->sms_code = null;    // Clear the sms_code after verification
//     $customer->save();

//   $token =$customer->createToken($request->header('X-Client-Device-Name'))->plainTextToken;
//         $data = [
//             'user'=>new CustomerResource($customer),
//             "token"=>$token,
//         ];
//         return $this->sendResponse($data,\Lang::get("lang.valid_otp"));
// }





 public function verifySmsCode(Request $request){
        $this->validate($request,[
            "code"=>"required",
            "token"=>"required"
        ]);



        $getCustomer = Customer::where('sms_code',$request->code)
            ->where('sms_verify',false)
            ->where('token',$request->token)

            ->first();
        if(is_null($getCustomer)){
            return $this->sendError(\Lang::get("lang.wrong_otp"),422);
        }

        $getCustomer->sms_verify = true;

        $getCustomer->save();
        $token =$getCustomer->createToken($request->header('X-Client-Device-Name'))->plainTextToken;
        $data = [
            'user'=>new CustomerResource($getCustomer),
            "token"=>$token,
        ];
        return $this->sendResponse($data,\Lang::get("lang.valid_otp"));


    }


//   public function smsCode(Request $request){
//       // dd($this->CURRENT_USER);
//         $this->validate($request,[
//             'sms_code'=>'required'
//         ]);

//         if ($this->CURRENT_USER->sms_code == $request->sms_code) {

//             $this->CURRENT_USER->sms_verify=true;
//             $this->CURRENT_USER->save();
//             $token =$this->CURRENT_USER->createToken($request->header('X-Client-Device-Name'))->plainTextToken;
//             $result = [
//                 'user'=>$this->CURRENT_USER,
//                 'token'=>$token,
//             ];
//             return $this->sendResponse($result,'');

//         }


//         return $this->sendError(\Lang::get("lang.wrong_otp"),422);



//     }

    public function smsCode(Request $request){
       // dd($this->CURRENT_USER);
        $this->validate($request,[
            'sms_code'=>'required'
        ]);

        if ($this->CURRENT_USER->sms_code == $request->sms_code) {

            $this->CURRENT_USER->sms_verify=true;
            $this->CURRENT_USER->save();
            $token =$this->CURRENT_USER->createToken($request->header('X-Client-Device-Name'))->plainTextToken;
            $result = [
                'user'=>$this->CURRENT_USER,
                'token'=>$token,
            ];
            return $this->sendResponse($result,'');

        }


        return $this->sendError(\Lang::get("lang.wrong_otp"),422);



    }

    public  function smsResend(Request $request){
        $this->validate($request,[
            "phone"=>"required",
            "phone_code"=>"required",
        ]);


        $user = Customer::where("full_mobile",getFullMobile($request->phone_code,$request->phone))->first();
        if (is_null($user)){
            return $this->sendError(\Lang::get("lang.not_found"),422);
        }


        if ($user->phone_code =="90"){//TR
            $sms_code = "111111" ;
        }else
            $sms_code=generateNDigitRandomNumber(6);

        $token=\Str::random(32);
        $user->sms_code =$sms_code;
        $user->sms_verify =false;
        $user->token =$token;
        $user->save();

        /*$msg ="اهلا بكم في تطبيق WinToo";
        $msg .= "\n";
        $msg .="رمز التحقق :";*/
        $msg = $sms_code;

        send_smsMobile($user->phone,$msg,$user , "verify");

        return $this->sendResponse([
            "token"=>$token
        ],\Lang::get("lang.code_resent"));
    }

    public function fcmSend(Request $request){

        $this->validate($request, [
            'device_type' =>'required',
            'fcm_token'=>'required',
        ]);
        $this->CURRENT_USER->device_type =$request->device_type;
        $this->CURRENT_USER->fcm_token =$request->fcm_token;
        $this->CURRENT_USER->save();
        return $this->sendResponse($this->CURRENT_USER,'');
    }

    public function set_forget_password(Request $request){
        $this->validate($request, ['new_password' => 'numeric|required']);
        $passwordLast = bcrypt($request->new_password);
        $this->CURRENT_USER->password=$passwordLast;
        $this->CURRENT_USER->save();

        return $this->sendResponse(null,\Lang::get("lang.password_changed"));


    }

    public function ForgetPassword(Request $request){
        $this->validate($request, ['old_password' => 'required','new_password' => 'required']);


        $old_password = $request->old_password;
        if(Hash::check($old_password, $this->CURRENT_USER->password)){

            $passwordLast = bcrypt($request->new_password);
            $this->CURRENT_USER->password=$passwordLast;
            $this->CURRENT_USER->save();
            return $this->sendResponse(null,\Lang::get("lang.password_changed"));
        }

        return $this->sendError(\Lang::get("lang.invalid_old_password"),422);

    }

    public function deleteAccount (Request $request){

        $customer = Customer::find($this->CURRENT_USER->id);
        $customer->phone = $customer->phone."_delete".$this->CURRENT_USER->id;
        $customer->full_mobile = $customer->full_mobile."_delete".$this->CURRENT_USER->id;
        $customer->email = $customer->email."_delete".$this->CURRENT_USER->id;
        $customer->save();
        $customer->delete();
        return $this->sendResponse(null,\Lang::get("lang.account_deleted"));

    }






}
