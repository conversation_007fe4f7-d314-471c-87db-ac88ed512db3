<?php
namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Customer;
use Kreait\Firebase\Factory;
use Kreait\Firebase\Messaging\CloudMessage;
use Kreait\Firebase\Messaging\Notification;

class CometChatController extends Controller
{
    // Handle the CometChat Webhook
    public function handleWebhook(Request $request)
    {
        try {
            // Log the entire request payload for debugging
            logger()->info('Webhook Payload: ' . json_encode($request->all()));

            // Get the trigger type (message or call)
            $trigger = $request->input('trigger');

            // Get the message data from the webhook
            $message = $request->input('data.message');

            // Check if it's a message or call event
            if ($trigger === 'message_sent') {
                // Handle message or call
                if ($message['category'] === 'message') {
                    // Process the message notification
                    $this->sendMessageNotification($message);
                } elseif ($message['category'] === 'call') {
                    // Process the call notification
                    $this->sendCallNotification($message);
                } else {
                    return response()->json([
                        'status' => 'failed',
                        'message' => 'Unknown message category'
                    ], 400);
                }
                return response()->json(['status' => 'success']);
            }

            // // Return error if the trigger is not recognized
            // return response()->json([
            //     'status' => 'failed',
            //     'message' => 'Invalid trigger type'
            // ], 400);

        } catch (\Exception $e) {
            // Log the error and return the response with the error message
            logger()->error('Error handling CometChat webhook: ' . $e->getMessage());
            return response()->json([
                'status' => 'failed',
                'message' => 'Error processing the webhook: ' . $e->getMessage()
            ], 500);
        }
    }

// Send message notification
public function sendMessageNotification($message)
{
    try {
        // Extract sender and receiver IDs
        $senderId = $this->extractIdFromUid($message['data']['entities']['sender']['entity']['uid']);
        $receiverId = $this->extractIdFromUid($message['data']['entities']['receiver']['entity']['uid']);
        $messageText = $message['data']['text'];
        $senderName = $message['data']['entities']['sender']['entity']['name'];

        // Log the extracted data
        logger()->info("Sender ID: $senderId, Receiver ID: $receiverId, Sender Name: $senderName, Message Text: $messageText");

        // Check if sender and receiver IDs are valid
        if (empty($senderId) || empty($receiverId)) {
            throw new \Exception("Invalid sender or receiver ID");
        }

        // Find the receiver in your system (e.g., Customer model)
        $receiver = Customer::find($receiverId);

        if (!$receiver) {
            throw new \Exception("Receiver not found for user ID: $receiverId");
        }

        if (empty($receiver->fcm_token)) {
            throw new \Exception("Receiver FCM token is missing for user ID: $receiverId");
        }

        // Create the message with the format "UserName: MessageText"
        $notificationMessage = "$senderName: $messageText";

        // Send a Firebase notification for the message
        $this->sendFirebaseNotification($receiver, $notificationMessage, $senderId);

    } catch (\Exception $e) {
        // Log the error and continue the process
        logger()->error('Error sending message notification: ' . $e->getMessage());
        throw $e; // re-throw to be handled at the calling function level
    }
}


    // Handle call notification
public function sendCallNotification($message)
{
    try {
        // Extract sender and receiver details
        $senderId = $this->extractIdFromUid($message['data']['entities']['by']['entity']['uid']);
        $receiverId = $this->extractIdFromUid($message['data']['entities']['for']['entity']['uid']);
        $senderName = $message['data']['entities']['by']['entity']['name'];

        logger()->info('Extracted call sender ID: ' . $senderName);
        logger()->info('Extracted call receiver ID: ' . $receiverId);

        // Check if sender and receiver IDs are valid
        if (empty($senderId) || empty($receiverId)) {
            throw new \Exception("Invalid sender or receiver ID for call");
        }

        // Find the receiver in your system
        $receiver = Customer::find($receiverId);
        if (!$receiver || empty($receiver->fcm_token)) {
            throw new \Exception("Receiver not found or FCM token is missing for user ID: $receiverId");
        }

        // Handle call action
        $callAction = $message['data']['action'];
        $notificationTitle = "Call from $senderName";
        $notificationBody = "You have an incoming call from $senderName.";

        // Adjust notification based on call action
        if ($callAction === 'answered') {
            $notificationTitle = 'Call Answered';
            $notificationBody = "You answered a call from $senderName.";
        } elseif ($callAction === 'rejected') {
            $notificationTitle = 'Call Rejected';
            $notificationBody = "$senderName rejected the call.";
        } elseif ($callAction === 'unanswered') {
            $notificationTitle = 'Missed Call';
            $notificationBody = "You missed a call from $senderName.";
        }

        // Send Firebase notification with custom data for call handling in Flutter
        $this->sendFirebaseNotification($receiver, $notificationBody, $senderName, [
            'call_type' => 'audio',  // or 'video' depending on the call type
            'call_action' => $callAction,  // 'answered', 'rejected', 'unanswered'
            'sender_id' => $senderId,
            'sender_name' => $senderName,
        ]);

    } catch (\Exception $e) {
        logger()->error('Error sending call notification: ' . $e->getMessage());
        throw $e;
    }
}


    // Send Firebase notification
public function sendFirebaseNotification($receiver, $messageText, $senderId, $customData = [])
{
    try {
        // Initialize Firebase with service account
        $firebase = (new Factory())->withServiceAccount(base_path(env('FIREBASE_CREDENTIALS')));
        $messaging = $firebase->createMessaging();

        // Create a notification
        $notification = Notification::create('New Message ', $messageText);

        // Create the data payload
        $data = array_merge([
            'title' => 'New Call',
            'body' => $messageText,
            'sender_id' => $senderId,
            'type' => 'call',  // Identify this as a call notification
            'deeplink_url' => 'myapp://chat?chatId=' . $senderId, // Deep link to the chat screen
        ], $customData);  // Merge custom data into the payload

        // Create a Firebase Cloud Message with the receiver's FCM token
        $firebaseMessage = CloudMessage::withTarget('token', $receiver->fcm_token)
            ->withNotification($notification)
            ->withData($data);

        // Send the notification
        $messaging->send($firebaseMessage);
        logger()->info("Call notification sent successfully to user ID: $receiver->id");

    } catch (\Throwable $e) {
        logger()->error('Error sending Firebase notification: ' . $e->getMessage());
        throw new \Exception('Failed to send Firebase notification: ' . $e->getMessage());
    }
}


    // Helper function to extract the numeric ID from 'user_1347' style UID
    private function extractIdFromUid($uid)
    {
        if (strpos($uid, 'user_') === 0) {
            // Extract numeric part of the UID
            $numericId = str_replace('user_', '', $uid);

            // Check if the remaining part is a valid number
            if (is_numeric($numericId)) {
                return $numericId;
            } else {
                logger()->warning("Invalid numeric ID in UID format: $uid");
                return null;
            }
        } else {
            logger()->warning("Unexpected UID format: $uid");
            return null;
        }
    }
}
