<?php

namespace App\Http\Controllers\Api;
use Carbon\Carbon;
use App\Http\Controllers\Controller;
use App\Http\Resources\AreaResource;
use App\Http\Resources\CurrencyResource;
use App\Http\Resources\FaqResource;
use App\Http\Resources\GeneralResource;
use App\Http\Resources\JobRequestResource;
use App\Http\Resources\AddAdsResource;
use App\Http\Resources\NotificationApiResource;
use App\Http\Resources\ProductRequestResource;
use App\Http\Resources\RentalRequestResource;
use App\Http\Resources\SystemDrawDashboardResource;
use App\Http\Resources\SystemDrawResource;
use App\Http\Resources\SystemDrawStoreResource;
use App\Models\Attribute;
use App\Models\City;
use App\Models\Comment;
use App\Models\Country;
use App\Models\Currency;
use App\Models\Faq;
use App\Models\Follower;
use App\Models\Governorate;
use App\Models\JobRequest;
use App\Models\AdsRequest;
use App\Models\JobType;
use App\Models\Offer;
use App\Models\PostMedia;
use App\Models\ProductRequest;
use App\Models\ProductRequestAttribute;
use App\Models\ProductRequestsMedia;
use App\Models\QRRequest;
use App\Models\Region;
use App\Models\RentalRequest;
use App\Models\RentalRequestAttribute;
use App\Models\RentalRequestsMedia;
use App\Models\Store;
use App\Models\SystemDraw;
use App\Services\SendNotification;
use Illuminate\Http\Request;
use App\Http\Controllers\BaseControllerApi;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Hash;
use Kreait\Firebase\Factory;
use Kreait\Firebase\Messaging\CloudMessage;
use Kreait\Firebase\Messaging\Notification as FirebaseNotification;
use App\Http\Resources\PostResource;

use App\Models\Customer;
use Illuminate\Support\Facades\Lang;

class GeneralApiController extends BaseControllerApi
{
    public  function getArea(Request $request){

        if($request->governorate_id){
            $cites = City::where('governorate_id',$request->governorate_id)->get();
        }
        elseif($request->country_id){
            $cites = Governorate::where('country_id',$request->country_id)->get();
        } elseif($request->city_id){
            $cites = Region::where('city_id',$request->city_id)->get();
        }else{
            $cites = Country::all();
        }
        $result= [
            'area'=>AreaResource::collection($cites)
        ];

        return $this->sendResponse($result,'');
    }
    public  function getCurrencies(Request $request){

        $currencies = Currency::Active()->get();

        $result= [
            'currencies'=>CurrencyResource::collection($currencies)
        ];

        return $this->sendResponse($result,'');
    }

    public function faqs(Request $request){
        $result = [
            'faqs'=>FaqResource::collection(Faq::all())
        ];

        return $this->sendResponse($result,'');
    }

    public function setting(Request $request){
        $offer = Offer::isBlack()->first();
        $result = [
            'about_us'=>\App\Models\Page::find(2),
            'policy'=>\App\Models\Page::find(5),
            'condition'=>\App\Models\Page::find(6),
            'setting'=>\App\Models\Setting::first(),
            "black_friday" =>[
                "is_active"=>$offer?true:false,
                "image_url"=>$offer?$offer->image_url:null,
            ],
        ];

        return $this->sendResponse($result,'');


    }

    public function notification(Request $request){
        $currentUser = auth('customers')->user();
        $notifications = $currentUser->notifications()->paginate(12);
        $currentUser->unreadNotifications->markAsRead();

        $result = [
            'notifications'=>NotificationApiResource::collection($notifications),
            "pagination"=> [
                "i_total_objects"=>$notifications->count(),
                "i_items_on_page"=> $notifications->count(),
                "i_per_pages"=>$notifications->perPage(),
                "i_current_page"=>$notifications->currentPage() ,
                "i_total_pages"=> $notifications->total()
            ]];
        return $this->sendResponse($result,null);

    }

    public function notifications_count(Request $request){

        return $this->sendResponse(["notifications_unread" =>  (int)$this->CURRENT_USER->unreadNotifications->count()],null);

    }



    public function storeDraws(Request $request){
        $currentUser = auth('customers')->user();
        $draws = SystemDraw::whereNotNull("store_id")->Where(function ($query){

       return     $query->where(function ($query){

                $query->hasWinners() ;

            })->orWhere(function ($query){

                $query->isActive() ;

            });

        })->orderBy('created_at', 'desc')->paginate(12);
        $result = [
            'draws'=>SystemDrawStoreResource::collection($draws),
            "pagination"=> [
                "i_total_objects"=>$draws->count(),
                "i_items_on_page"=> $draws->count(),
                "i_per_pages"=>$draws->perPage(),
                "i_current_page"=>$draws->currentPage() ,
                "i_total_pages"=> $draws->total()
            ]];
        return $this->sendResponse($result,null);

    }

      public function storeDrawsActive(Request $request){
        // $currentUser = auth('customers')->user();
        $draws = SystemDraw::whereNotNull("store_id")->Where("is_end","==",false)->orderBy('created_at', 'desc')->get();
        $result = [
            'draws'=>SystemDrawStoreResource::collection($draws),
            // "pagination"=> [
            //     "i_total_objects"=>$draws->count(),
            //     "i_items_on_page"=> $draws->count(),
            //     "i_per_pages"=>$draws->perPage(),
            //     "i_current_page"=>$draws->currentPage() ,
            //     "i_total_pages"=> $draws->total()
            // ]
            ];
        return $this->sendResponse($result,null);

    }


    public function qrCodeScan(Request $request){
        $this->validate(
            $request,
            [
                'qr_code' => "required|exists:stores,id",
            ]
        );
        $currentUser = auth('customers')->user();
        $store = Store::find($request->qr_code);
        $qr_code = QRRequest::create([
            "customer_id"=>$currentUser->id,
            "store_id"=>$store->id,
            "status"=>"New",
        ]);
        SendNotification::qrScanFromCustomerToStoreNotification($qr_code);

        $msg = Lang::get("lang.qr_scanned_for_store")." ".$store->name;


        $follower = Follower::where("store_id",$request->store_id)
            ->where("customer_id",$this->CURRENT_USER->id)
            ->first();

        if(!$follower){

            $create = Follower::create([
                "store_id"=>$store->id,
                "customer_id"=>$currentUser->id
            ]);

        }

        return $this->sendResponse($qr_code,$msg);
    }


    public function productRequestV2(Request $request){

        if ($request->type =="buy") {
            $this->validate(
                $request,
                [
                   // 'product_name' => "required",
                    'category_id' => "required",
                    'type' => "required",
                    'buy_price_from' => "required|numeric",
                    'buy_price_to' => "required|numeric",
                ],[],[

                    "buy_price_from" => \Lang::get("lang.buy_price_from") ,
                    "buy_price_to" => \Lang::get("lang.buy_price_to") ,
                ]
            );
        }else if ($request->type =="sell"){
            $this->validate(
                $request,
                [
                  //  'product_name' => "required",

                    'category_id' => "required",
                    'type' => "required",
                    'sell_price' => "required|numeric",
                ],[],[

                    "sell_price" => \Lang::get("lang.sell_price") ,
                ]
            );
        }else{
            $this->validate(
                $request,
                [
                    'category_id' => "required",
                    'type' => "required",
                ]
            );
        }


        $currentUser = auth('customers')->user();
        $currentStore = auth('store')->user();

        $create = ProductRequest::create([
         //   "product_name"=>$request->product_name,
          //  "description"=>$request->description,
            "store_category_id"=>$request->category_id,
            "customer_id"=>$currentUser?$currentUser->id:null,
            "store_id"=>$currentStore?$currentStore->id:null,
            "sell_price"=>$request->type=="sell" ? $request->sell_price :0,
            "buy_price_from"=>$request->type=="buy" ? $request->buy_price_from:0,
            "buy_price_to"=>$request->type=="buy" ? $request->buy_price_to:0,
            "type"=>$request->type,
            "country_id"=>optional(Governorate::find($request->governorate_id))->country_id,
            "governorate_id"=>$request->governorate_id,
            "city_id"=>$request->city_id,
            "region_id"=>$request->region_id,
            "currency_id"=>$request->currency_id,
          //  "price"=>$request->price,
           // "hash"=>md5(json_encode([$request->category_id , $request->attributes_list]))
        ]);

        $toHash_attrs = [] ;

        foreach ($request->attributes_list as $attribute){
            $attribute_object  =Attribute::find($attribute["id"]);

            if ($attribute_object->type != "range")
                $toHash_attrs[]=$attribute ;

            ProductRequestAttribute::create([
                "attribute_id"=>$attribute["id"],
                "product_id"=>$create->id,
                "value"=>is_array($attribute["value"])?json_encode($attribute["value"]):$attribute["value"],
                "payload"=>json_encode($attribute_object),

            ]);
        }

        $create->hash = md5(json_encode([$request->category_id , $toHash_attrs])) ;
        $create->save();



        if ($request->media)
            foreach ($request->media as $row) {
                $filename = $row->store('/', 'public');
                ProductRequestsMedia::create([
                    'request_id' => $create->id,
                    'image' => $filename,
                ]);
            }

        $create->getMatchingProductsRequestsAndNotify(true);



        return $this->sendResponse(new ProductRequestResource(ProductRequest::find($create->id)),null);
    }
    public function editProductRequestV2(Request $request ,$product_request_id){
        if ($request->type =="buy") {
            $this->validate(
                $request,
                [
                   // 'product_name' => "required",

                    'category_id' => "required",
                    'type' => "required",
                    'buy_price_from' => "required|numeric",
                    'buy_price_to' => "required|numeric",
                ],[],[

                    "buy_price_from" => \Lang::get("lang.buy_price_from") ,
                    "buy_price_to" => \Lang::get("lang.buy_price_to") ,
                ]
            );
        }else if ($request->type =="sell"){
            $this->validate(
                $request,
                [

                 //   'product_name' => "required",

                    'category_id' => "required",
                    'type' => "required",
                    'sell_price' => "required|numeric",
                ],[],[

                    "sell_price" => \Lang::get("lang.sell_price") ,
                ]
            );
        }else{
            $this->validate(
                $request,
                [
                    'category_id' => "required",
                    'type' => "required",
                ]
            );
        }


        $currentUser = auth('customers')->user();
        $product_request =  ProductRequest::where(function ($query){

            $currentUser = auth('customers')->user();
            $currentStore = auth('store')->user();

            if ($currentUser)
                $query->where("customer_id",$currentUser->id);

            if ($currentStore)
                $query->where("store_id",$currentStore->id);


        })->where("id",$product_request_id)->first();

        $currentStore = auth('store')->user();

        $product_request->update([
            //"product_name"=>$request->product_name,
          //  "description"=>$request->description,
            "store_category_id"=>$request->category_id,
            "customer_id"=>$currentUser?$currentUser->id:null,
            "store_id"=>$currentStore?$currentStore->id:null,

            "sell_price"=>$request->type=="sell" ? $request->sell_price :0,
            "buy_price_from"=>$request->type=="buy" ? $request->buy_price_from:0,
            "buy_price_to"=>$request->type=="buy" ? $request->buy_price_to:0,
            "type"=>$request->type,

            "country_id"=>optional(Governorate::find($request->governorate_id))->country_id,
            "governorate_id"=>$request->governorate_id,
            "city_id"=>$request->city_id,
            "region_id"=>$request->region_id,
            "currency_id"=>$request->currency_id,

          //  "price"=>$request->price,
         //   "hash"=>md5(json_encode([$request->category_id , $request->attributes_list]))
        ]);

        ProductRequestAttribute::where("product_id" , $product_request_id)->delete();
        $toHash_attrs = [];
        foreach ($request->attributes_list as $attribute){
            $attribute_object  =Attribute::find($attribute["id"]);
            if ($attribute_object->type != "range")
                $toHash_attrs[]=$attribute ;

            ProductRequestAttribute::create([
                "attribute_id"=>$attribute["id"],
                "product_id"=>$product_request->id,
                "value"=>is_array($attribute["value"])?json_encode($attribute["value"]):$attribute["value"],
                "payload"=>json_encode($attribute_object),

            ]);
        }



        $product_request->hash = md5(json_encode([$request->category_id , $toHash_attrs])) ;
        $product_request->save();



        if ($request->media)
            foreach ($request->media as $row) {
                $filename = $row->store('/', 'public');
                ProductRequestsMedia::create([
                    'request_id' => $product_request->id,
                    'image' => $filename,
                ]);
            }



        //

         $product_request->getMatchingProductsRequestsAndNotify(true);


        return $this->sendResponse(new ProductRequestResource(ProductRequest::find($product_request->id)),null);
    }
    public function productRequestDelete(Request $request ,$product_request_id ){

        $productRequest = ProductRequest::where(function ($query){

            $currentUser = auth('customers')->user();
            $currentStore = auth('store')->user();

            if ($currentUser)
                $query->where("customer_id",$currentUser->id);

            if ($currentStore)
                $query->where("store_id",$currentStore->id);


        })->where("id",$product_request_id)->first();

        if(is_null($productRequest) ){
            return $this->sendError('Product Request not found',404);
        }elseif ($productRequest->customer_id != $this->CURRENT_USER->id  && $productRequest->store_id != $this->CURRENT_USER->id ){
            return $this->sendError('You do not have permission to delete this  item',401 );
        }

        $productRequest->delete();

        return $this->sendResponse(null, \Lang::get("lang.deleted_successfully"));

    }
    public function productRequestImageDelete(Request $request)
    {
        $this->validate(
            $request,
            [
                'product_request_id' => "required|exists:product_requests,id",
                'image_id' => "required",

            ]
        );

        $product_request_media = ProductRequestsMedia::where("id", $request->image_id)
            ->where("request_id", $request->product_request_id)
            ->first();

        if (is_null($product_request_media)) {
            return $this->sendError(\Lang::get("lang.invalid_order"), 422);
        }
        $product_request_media->delete();
        return $this->sendResponse(null, \Lang::get("lang.deleted_successfully"));
    }
    public function myProductList(Request $request){

        if($request->type){
            $product_requests = ProductRequest::where("type",$request->type)->where(function ($query){

                $currentUser = auth('customers')->user();
                $currentStore = auth('store')->user();

                if ($currentUser)
                $query->where("customer_id",$currentUser->id);

                if ($currentStore)
                $query->where("store_id",$currentStore->id);


            })
           ->orderBy("id","desc")->paginate(10);
        }else
        $product_requests = ProductRequest::where("customer_id",$this->CURRENT_USER->id)->orderBy("id","desc")->paginate(10);




       // $result = ($product_requests);

        $result = [
            'requests'=>ProductRequestResource::collection($product_requests),
            "pagination"=> [
                "i_total_objects"=>$product_requests->count(),
                "i_items_on_page"=> $product_requests->count(),
                "i_per_pages"=>$product_requests->perPage(),
                "i_current_page"=>$product_requests->currentPage() ,
                "i_total_pages"=> $product_requests->total()
            ]];
        return $this->sendResponse($result,null);
    }
    public function myProductRequestDetails(Request $request,$product_request_id){
        $product_request = ProductRequest::where(function ($query){

            $currentUser = auth('customers')->user();
            $currentStore = auth('store')->user();

            if ($currentUser)
                $query->where("customer_id",$currentUser->id);

            if ($currentStore)
                $query->where("store_id",$currentStore->id);


        })->where("id",$product_request_id)->first();
        /* $result = [
             'requests'=>ProductRequestResource::collection($product_requests),
             "pagination"=> [
                 "i_total_objects"=>$product_requests->count(),
                 "i_items_on_page"=> $product_requests->count(),
                 "i_per_pages"=>$product_requests->perPage(),
                 "i_current_page"=>$product_requests->currentPage() ,
                 "i_total_pages"=> $product_requests->total()
             ]];*/
        if ($product_request)
            return $this->sendResponse(new ProductRequestResource($product_request),null);
        else
            return $this->sendError(\Lang::get("lang.invalid_order"));


    }
    public function productRequestDetails(Request $request,$product_request_id){
        $product_request = ProductRequest::find($product_request_id);
        /* $result = [
             'requests'=>ProductRequestResource::collection($product_requests),
             "pagination"=> [
                 "i_total_objects"=>$product_requests->count(),
                 "i_items_on_page"=> $product_requests->count(),
                 "i_per_pages"=>$product_requests->perPage(),
                 "i_current_page"=>$product_requests->currentPage() ,
                 "i_total_pages"=> $product_requests->total()
             ]];*/
        if ($product_request)
            return $this->sendResponse(new ProductRequestResource($product_request),null);
        else
            return $this->sendError(\Lang::get("lang.invalid_order"));


    }



    public function myJobRequests(Request $request){

        if($request->type){
            $product_requests = JobRequest::where("type",$request->type)->where(function ($query){

                $currentUser = auth('customers')->user();
                $currentStore = auth('store')->user();

                if ($currentUser)
                $query->where("customer_id",$currentUser->id);

                if ($currentStore)
                $query->where("store_id",$currentStore->id);


            })
           ->orderBy("id","desc")->paginate(10);
        }else
        $product_requests = JobRequest::where("customer_id",$this->CURRENT_USER->id)->orderBy("id","desc")->paginate(10);




       // $result = ($product_requests);

        $result = [
            'requests'=>JobRequestResource::collection($product_requests),
            "pagination"=> [
                "i_total_objects"=>$product_requests->count(),
                "i_items_on_page"=> $product_requests->count(),
                "i_per_pages"=>$product_requests->perPage(),
                "i_current_page"=>$product_requests->currentPage() ,
                "i_total_pages"=> $product_requests->total()
            ]];
        return $this->sendResponse($result,null);
    }

//     public function ads(Request $request){
//
//        $sevenDaysAgo = Carbon::now()->subDays(7)->toDateString();
//       $currentUser = auth('customers')->user()->country_id;
//       $currentUserStore = auth('store')->user()->country_id;
//
//                       $product_requests = AdsRequest::whereDate('paid_ads.created_at', '>=', $sevenDaysAgo)->where('deleted', '=', false)
//    // ->where('stores.country_id', $currentUser)
//    // ->whereDate('paid_ads.created_at', '>=', $sevenDaysAgo)
//    ->inRandomOrder()
//    ->paginate(3000);
//
//
//       // Filter By Country
//
//    //     $product_requests;
//    //   if($currentUser !=null)
//    //   {
//    //              $product_requests = AdsRequest::join('stores', 'paid_ads.customer_id', '=', 'stores.id')
//    // ->where('stores.country_id', $currentUser)
//    // ->whereDate('paid_ads.created_at', '>=', $sevenDaysAgo)
//    // ->inRandomOrder()
//    // ->paginate(3000);
//
//    //   }
//    //   else
//    //   if($currentUserStore !=null)
//    //   {
//    //                   $product_requests = AdsRequest::join('stores', 'paid_ads.customer_id', '=', 'stores.id')
//    // ->where('stores.country_id', $currentUserStore)
//    // ->whereDate('paid_ads.created_at', '>=', $sevenDaysAgo)
//    // ->inRandomOrder()
//    // ->paginate(3000);
//    //   }
//
//
//    //   $product_requests = AdsRequest::whereDate('created_at', '>=', $sevenDaysAgo)
//    // ->inRandomOrder()
//    // ->paginate(3000);
//
//
//
//
//       // $result = ($product_requests);
//
//        $result = [
//            'requests'=>AddAdsResource::collection($product_requests),
//
//            ];
//        return $this->sendResponse($result,'');
//    }


    public function ads(Request $request){

        $sevenDaysAgo = Carbon::now()->subDays(7)->toDateString();

        $product_requests = AdsRequest::whereDate('paid_ads.created_at', '>=', $sevenDaysAgo)->where('deleted', '=', false)
            ->inRandomOrder()
            ->paginate(3000);

        $result = [
            'requests'=>AddAdsResource::collection($product_requests),

        ];
        return $this->sendResponse($result,'');
    }

    public function myAds(Request $request){

        $sevenDaysAgo = Carbon::now()->subDays(7)->toDateString();
       $currentUser = auth('customers')->user()->country_id;
       $currentUserStore = auth('store')->user()->id;

                       $product_requests = AdsRequest::where('customer_id', '=', $currentUserStore)->where('deleted', '=', false)->orderBy("created_at","desc")->get();


        $result = [
            'requests'=>AddAdsResource::collection($product_requests),

            ];
        return $this->sendResponse($result,'');
    }
     public function deleteAd(Request $request){

       $adRequestToDelete = AdsRequest::where('id',"=", $request->adId)
        ->update(['deleted'=> true]); // Fetch the ad request or throw a 404 error if not found



// $adRequestToDelete->update(['deleted', "=", true]);
return $this->sendResponse($result,$adRequestToDelete);
    // Soft delete the ad request
    // $adRequestToDelete->update(['deleted' => true]);
        return $this->sendResponse($result,'');
    }
    public function jobRequestDetails(Request $request , $job_request_id){

        $job_request = JobRequest::find($job_request_id);

        return $this->sendResponse(new JobRequestResource($job_request),null);
    }
    public function recommendedJobRequests(Request $request){


            $product_requests = JobRequest::where("type","offer")->where(function ($query){

                $currentUser = auth('customers')->user();
                $currentStore = auth('store')->user();

                if ($currentUser){

                    $query->where(function ($q) use ($currentUser){

                        $q->where(function ($q) use($currentUser){

                            $q->where("customer_id","!=",$currentUser->id)
                                ->whereIn("customer_id" ,function ($q){
                                    $q->select('id')
                                        ->from("customers")
                                    ;
                                });


                        })
                            ->orWhereNull("customer_id");
                    });

                }

                if ($currentStore){

                    $query->where(function ($q) use ($currentStore){

                        $q->where(function ($q) use($currentStore){

                            $q->where("store_id","!=",$currentStore->id)
                                ->whereIn("store_id" ,function ($q){
                                    $q->select('id')
                                        ->from("stores")
                                    ;
                                });


                        });

                        $q->orWhereNull("store_id");
                    });

                }



            })
           ->inRandomOrder()->paginate(10);


       // $result = ($product_requests);

        $result = [
            'requests'=>JobRequestResource::collection($product_requests),
            "pagination"=> [
                "i_total_objects"=>$product_requests->count(),
                "i_items_on_page"=> $product_requests->count(),
                "i_per_pages"=>$product_requests->perPage(),
                "i_current_page"=>$product_requests->currentPage() ,
                "i_total_pages"=> $product_requests->total()
            ]];
        return $this->sendResponse($result,null);
    }



    public function getRandomProductRequests(Request $request){



        $product_requests = ProductRequest::where("type","sell")->orderBy("id","desc")->inRandomOrder($request->session()->get("productsRequestsSeed"))->paginate(10);

       // $result = ($product_requests);

        $result = [
            'requests'=>ProductRequestResource::collection($product_requests),
            "pagination"=> [
                "i_total_objects"=>$product_requests->count(),
                "i_items_on_page"=> $product_requests->count(),
                "i_per_pages"=>$product_requests->perPage(),
                "i_current_page"=>$product_requests->currentPage() ,
                "i_total_pages"=> $product_requests->total()
            ]];
        return $this->sendResponse($result,null);
    }
    public function getRandomRentalRequests(Request $request){

        $rental_requests = RentalRequest::where("type",RentalRequest::$TYPE_OFFER)->orderBy("id","desc")->inRandomOrder($request->session()->get("rentalRequestsSeed"))->paginate(10);

       // $result = ($rental_requests);

        $result = [
            'requests'=>RentalRequestResource::collection($rental_requests),
            "pagination"=> [
                "i_total_objects"=>$rental_requests->count(),
                "i_items_on_page"=> $rental_requests->count(),
                "i_per_pages"=>$rental_requests->perPage(),
                "i_current_page"=>$rental_requests->currentPage() ,
                "i_total_pages"=> $rental_requests->total()
            ]];
        return $this->sendResponse($result,null);
    }



    public function addJobRequest(Request $request){
        if ($request->type =="request") {
            $this->validate(
                $request,
                [
                    'user_image' => "required",
                    'job_type_id' => "required",
                    'type' => "required",
                    'gender' => "required",
                    'age' => "required|numeric",
                    'exp_years' => "required|numeric",
                  //  'contact_number' => "required",

                ],[],[
                    "job_type_id" => \Lang::get("lang.job_type_id") ,
                    "type" => \Lang::get("lang.job_request_type") ,
                    "gender" => \Lang::get("lang.gender") ,
                    "age" => \Lang::get("lang.age") ,
                    "exp_years" => \Lang::get("lang.exp_years") ,

                ]
            );
        }else {


            $request->type = "offer" ;

            $this->validate(
                $request,
                [
                    //  'product_name' => "required",

                    'work_image' => "required",
                    'job_type_id' => "required",
                    'type' => "required",
                    'gender' => "required",
                    'offer_age_from' => "required|numeric",
                    'offer_age_to' => "required|numeric",
                    'offer_exp_years_from' => "required|numeric",
                    'offer_exp_years_to' => "required|numeric",
                    'salary' => "required|numeric",
                    'currency_id' => "required|numeric",
                   // 'contact_email' => "required",
                   // 'contact_number' => "required",

                ],[],[

                    "job_type_id" => \Lang::get("lang.job_type_id") ,
                    "type" => \Lang::get("lang.job_request_type") ,
                    "gender" => \Lang::get("lang.gender") ,
                    "offer_age_from" => \Lang::get("lang.offer_age_from") ,
                    "offer_age_to" => \Lang::get("lang.offer_age_to") ,
                    "offer_exp_years_from" => \Lang::get("lang.offer_exp_years_from") ,
                    "offer_exp_years_to" => \Lang::get("lang.offer_exp_years_to") ,
                    "salary" => \Lang::get("lang.salary") ,
                    "currency_id" => \Lang::get("lang.currency") ,
                ]
            );
        }


        $currentUser = auth('customers')->user();
        $currentStore = auth('store')->user();


        $user_image = $request->file('user_image')?$request->user_image->store('/','customer'):null;
        $work_image = $request->file('work_image')?$request->work_image->store('/','customer'):null;

        $create = JobRequest::create([

            "customer_id"=>$currentUser?$currentUser->id:null,
            "store_id"=>$currentStore?$currentStore->id:null,

            "job_type_id"=>$request->job_type_id,
            "user_image"=>$user_image,
            "work_image"=>$work_image,

            "age"=>$request->type=="request" ? $request->age :null,
            "offer_age_from"=>$request->type=="offer" ? $request->offer_age_from:null,
            "offer_age_to"=>$request->type=="offer" ? $request->offer_age_to:null,

            "exp_years"=>$request->type=="request" ? $request->exp_years :0,

            "offer_exp_years_from"=>$request->type=="offer" ? $request->offer_exp_years_from:0,
            "offer_exp_years_to"=>$request->type=="offer" ? $request->offer_exp_years_to:0,

            "gender"=>$request->gender,
            "type"=>$request->type,

            "contact_email"=>$request->contact_email,
            "contact_number"=>$request->contact_number,

            "salary"=>$request->salary,
            "currency_id"=>$request->currency_id,

            "country_id"=>optional(Governorate::find($request->governorate_id))->country_id,
            "governorate_id"=>$request->governorate_id,
            "city_id"=>$request->city_id,
            "region_id"=>$request->region_id,

            "target_country_id"=>optional(Governorate::find($request->target_governorate_id))->country_id,
            "target_governorate_id"=>$request->target_governorate_id,
            "target_city_id"=>$request->target_city_id,
            "target_region_id"=>$request->target_region_id,

        ]);


        $create->save();



        $create->getMatchingRequestsAndNotify(true);


        return $this->sendResponse(new JobRequestResource(JobRequest::find($create->id)),null);
    }

//    public function AddAds(Request $request){
//        //  $currentUser = auth('customers')->user();
//        //   $currentUserStore = auth('stores')->user();
//             $filename = null;
//
//        if ($request->media)
//            foreach ($request->media as $row) {
//                $filename = $row->store('/', 'public');
//                // PostMedia::create([
//                //     'post_id' => $create->id,
//                //     'image' => $filename,
//                // ]);
//            }
//
//$fcmTokens = Store::whereIn('id', [$request->customer_id])->whereNotNull('fcm_token')->pluck('fcm_token')->toArray();
//$customers1 = Store::whereIn('id', [$request->customer_id])->get()->first();
//        $create = AdsRequest::create([
//            "customer_id"=>$request->customer_id,
//            // "store_id"=>$currentStore?$currentStore->id:null,
//            // "job_type_id"=>$request->job_type_id,
//            // "user_image"=>$user_image,
//            // "work_image"=>$work_image,
//            "media"=>$filename ? $filename: "",
//            "title"=>$request->title,
//            "description"=>$request->description,
//            "city"=>$request->city,
//            "country"=>$request->country,
//
//
//
//        ]);
//
//// return $this->sendResponse($customers1,null);
//
//        $create->save();
//
//             SendNotification::sendAddPublishedNotification($customers1 , $create);
//
//        //             $factory = (new Factory())->withServiceAccount( base_path(env('FIREBASE_CREDENTIALS')));
//        //             $messaging = $factory->createMessaging();
//        //         $notification = FirebaseNotification::create("Advertisement status", "The Advertisement was added successfully");
//        //   foreach ( $customers1 as $customer){
//        //       $data=[
//        //           'title' =>"Advertisement status",
//        //           'body' => "The Advertisement was added successfully",
//        //           'id'=>null,
//        //           'type'=>'general_notification',
//        //           'link'=>''
//        //       ];
//
//        //           \Notification::send($customer,
//        //           new \App\Notifications\GeneralNotification(
//        //              "The Advertisement was added successfully",
//        //               'general_notification',
//        //               $data
//        //           ));
//        //   }
//
//
//
//        //   $message = CloudMessage::new()->withNotification($notification);
//
//        //     // Send notification to all FCM tokens in one statement
//        //     $batchSize = 150; // Adjust as needed
//
//        //     // Split FCM tokens into batches and send notifications
//        //     $chunks = array_chunk($fcmTokens, $batchSize);
//        //     foreach ($chunks as $chunk) {
//        //         try {
//        //             $messaging->sendMulticast($message, $chunk);
//        //             // echo "Notification sent successfully to batch of FCM tokens\n";
//        //         } catch (\Throwable $e) {
//        //              return $this->sendResponse($e,null);
//        //             // Handle errors
//        //             // echo "Error sending notification to batch of FCM tokens. Error: ".$e->getMessage()."\n";
//        //         }
//        //     }
//
//
//
//         return $this->sendResponse(new AddAdsResource(AdsRequest::find($create->id)),null);
//
//    }

//    public function AddAds(Request $request){
//        $filename = null;
//
//        if ($request->media)
//            foreach ($request->media as $row) {
//                $filename = $row->store('/', 'public');
//            }
//
//        $fcmTokens = Store::whereIn('id', [$request->customer_id])->whereNotNull('fcm_token')->pluck('fcm_token')->toArray();
//        $customers1 = Store::whereIn('id', [$request->customer_id])->get()->first();
//        $create = AdsRequest::create([
//            "customer_id"=>$request->customer_id,
//            "media"=>$filename ? $filename: "",
//            "title"=>$request->title,
//            "description"=>$request->description,
//            "city"=>$request->city,
//            "country"=>$request->country,
//        ]);
//        $create->save();
//        SendNotification::sendAddPublishedNotification($customers1 , $create);
//        return $this->sendResponse(new AddAdsResource(AdsRequest::find($create->id)),null);
//    }

    public function AddAds(Request $request){
        $filename = null;

        // Check if there is an active ad within the last 7 days
        $existingAd = AdsRequest::where('customer_id', $request->customer_id)
            ->where('created_at', '>=', now()->subDays(7))
            ->where('deleted', 0)
            ->first();

        if ($existingAd) {
            return response()->json([
                'message' => 'You already have an active ad. Please wait until it expires to create a new one.'
            ], 403); // 403 Forbidden status
        }

        // Handle file upload if media is provided
        if ($request->media) {
            foreach ($request->media as $row) {
                $filename = $row->store('/', 'public');
            }
        }

        // Get FCM tokens and customer details for notification
        $fcmTokens = Store::whereIn('id', [$request->customer_id])
            ->whereNotNull('fcm_token')
            ->pluck('fcm_token')
            ->toArray();
        $customers1 = Store::whereIn('id', [$request->customer_id])->first();

        // Create new ad request
        $create = AdsRequest::create([
            "customer_id" => $request->customer_id,
            "media" => $filename ? $filename : "",
            "title" => $request->title,
            "description" => $request->description,
            "city" => $request->city,
            "country" => $request->country,
        ]);

        // Save the ad request and send notification
        $create->save();
        SendNotification::sendAddPublishedNotification($customers1, $create);

        return $this->sendResponse(new AddAdsResource($create), null);
    }



    public function editJobRequest(Request $request , $job_request_id){

        if ($request->type =="request") {
            $this->validate(
                $request,
                [
                    // 'product_name' => "required",
                    'job_type_id' => "required",
                    'type' => "required",
                    'gender' => "required",
                    'age' => "required|numeric",
                    'exp_years' => "required|numeric",
                //    'contact_number' => "required",

                ],[],[

                    "job_type_id" => \Lang::get("lang.job_type_id") ,
                    "type" => \Lang::get("lang.job_request_type") ,
                    "gender" => \Lang::get("lang.gender") ,
                    "age" => \Lang::get("lang.age") ,
                    "exp_years" => \Lang::get("lang.exp_years") ,
                ]
            );
        }else if ($request->type =="offer"){
            $this->validate(
                $request,
                [
                    //  'product_name' => "required",

                    'job_type_id' => "required",
                    'type' => "required",
                    'gender' => "required",
                    'offer_age_from' => "required|numeric",
                    'offer_age_to' => "required|numeric",
                    'offer_exp_years_from' => "required|numeric",
                    'offer_exp_years_to' => "required|numeric",
                    'salary' => "required|numeric",
                    'currency_id' => "required|numeric",
                //    'contact_email' => "required",
                 //   'contact_number' => "required",

                ],[],[

                    "job_type_id" => \Lang::get("lang.job_type_id") ,
                    "type" => \Lang::get("lang.job_request_type") ,
                    "gender" => \Lang::get("lang.gender") ,
                    "offer_age_from" => \Lang::get("lang.offer_age_from") ,
                    "offer_age_to" => \Lang::get("lang.offer_age_to") ,
                    "offer_exp_years_from" => \Lang::get("lang.offer_exp_years_from") ,
                    "offer_exp_years_to" => \Lang::get("lang.offer_exp_years_to") ,
                    "salary" => \Lang::get("lang.salary") ,
                    "currency_id" => \Lang::get("lang.currency") ,
                    ]

            );
        }


        $currentUser = auth('customers')->user();
        $currentStore = auth('store')->user();


        $user_image = $request->file('user_image')?$request->user_image->store('/','customer'):null;
        $work_image = $request->file('work_image')?$request->work_image->store('/','customer'):null;

        $currentUser = auth('customers')->user();
        $job_request =  JobRequest::where(function ($query){

            $currentUser = auth('customers')->user();
            $currentStore = auth('store')->user();

            if ($currentUser)
                $query->where("customer_id",$currentUser->id);

            if ($currentStore)
                $query->where("store_id",$currentStore->id);


        })->where("id",$job_request_id)->first();

        $currentStore = auth('store')->user();

        $job_request->update([

            "customer_id"=>$currentUser?$currentUser->id:null,
            "store_id"=>$currentStore?$currentStore->id:null,

            "job_type_id"=>$request->job_type_id,
            "user_image"=>$user_image,
            "work_image"=>$work_image,

            "age"=>$request->type=="request" ? $request->age :null,
            "offer_age_from"=>$request->type=="offer" ? $request->offer_age_from:null,
            "offer_age_to"=>$request->type=="offer" ? $request->offer_age_to:null,

            "exp_years"=>$request->type=="request" ? $request->exp_years :0,
            "offer_exp_years_from"=>$request->type=="offer" ? $request->offer_exp_years_from:0,
            "offer_exp_years_to"=>$request->type=="offer" ? $request->offer_exp_years_to:0,

            "gender"=>$request->gender,
            "type"=>$request->type,

            "contact_email"=>$request->contact_email,
            "contact_number"=>$request->contact_number,

            "salary"=>$request->salary,
            "currency_id"=>$request->currency_id,

            "country_id"=>optional(Governorate::find($request->governorate_id))->country_id,
            "governorate_id"=>$request->governorate_id,
            "city_id"=>$request->city_id,
            "region_id"=>$request->region_id,

            "target_country_id"=>optional(Governorate::find($request->target_governorate_id))->country_id,
            "target_governorate_id"=>$request->target_governorate_id,
            "target_city_id"=>$request->target_city_id,
            "target_region_id"=>$request->target_region_id,

        ]);


        $job_request->getMatchingRequestsAndNotify(true);


        return $this->sendResponse(new JobRequestResource(JobRequest::find($job_request->id)),null);
    }


    public function jobRequestDelete(Request $request ,$job_request_id ){

        $jobRequest = JobRequest::where(function ($query){

            $currentUser = auth('customers')->user();
            $currentStore = auth('store')->user();

            if ($currentUser)
                $query->where("customer_id",$currentUser->id);

            if ($currentStore)
                $query->where("store_id",$currentStore->id);


        })->where("id",$job_request_id)->first();

        if(is_null($jobRequest) ){
            return $this->sendError('Request not found',404);
        }elseif ($jobRequest->customer_id != $this->CURRENT_USER->id  && $jobRequest->store_id != $this->CURRENT_USER->id ){
            return $this->sendError('You do not have permission to delete this  item',401 );
        }

        $jobRequest->delete();

        return $this->sendResponse(null, \Lang::get("lang.deleted_successfully"));

    }



    public function bop_send_to_page(Request $request){

    }



    /*
     *
     *


    job_type_id:1
    type:offer
    gender : male
    offer_age_from : 20
    "offer_age_to : 25
    offer_exp_years_from : 20
    offer_exp_years_to : 30
    currency_id : 5
    salary : 5
    contact_email :<EMAIL>
    contact_number :+972592106097


}


    */


    public function addRentalRequest(Request $request){

        if ($request->type ==RentalRequest::$TYPE_SEARCH) {
            $this->validate(
                $request,
                [
                    'name' => "required",
                    'category_id' => "required",
                    'type' => "required",
                    'search_price_from' => "required|numeric",
                    'search_price_to' => "required|numeric",
                ],[],[

                    "search_price_from" => \Lang::get("lang.search_price_from") ,
                    "search_price_to" => \Lang::get("lang.search_price_to") ,
                ]
            );
        }else if ($request->type ==RentalRequest::$TYPE_OFFER){
            $this->validate(
                $request,
                [
                      'name' => "required",

                    'category_id' => "required",
                    'type' => "required",
                    'offer_price' => "required|numeric",
                ],[],[

                    "offer_price" => \Lang::get("lang.offer_price") ,
                ]
            );
        }else{
            $this->validate(
                $request,
                [
                    'name' => "required",
                    'category_id' => "required",
                    'type' => "required",
                ]
            );
        }


        $currentUser = auth('customers')->user();
        $currentStore = auth('store')->user();

        $create = RentalRequest::create([
            "name"=>$request->name,
            //  "description"=>$request->description,
            "store_category_id"=>$request->category_id,
            "customer_id"=>$currentUser?$currentUser->id:null,
            "store_id"=>$currentStore?$currentStore->id:null,
            "offer_price"=>$request->type==RentalRequest::$TYPE_OFFER ? $request->offer_price :0,
            "search_price_from"=>$request->type==RentalRequest::$TYPE_SEARCH ? $request->search_price_from:0,
            "search_price_to"=>$request->type==RentalRequest::$TYPE_SEARCH ? $request->search_price_to:0,
            "type"=>$request->type,
            "country_id"=>optional(Governorate::find($request->governorate_id))->country_id,
            "governorate_id"=>$request->governorate_id,
            "city_id"=>$request->city_id,
            "region_id"=>$request->region_id,
            "rental_period"=>$request->rental_period,
            "currency_id"=>$request->currency_id,

            //  "price"=>$request->price,
            // "hash"=>md5(json_encode([$request->category_id , $request->attributes_list]))
        ]);

        $toHash_attrs = [] ;

        foreach ($request->attributes_list as $attribute){
            $attribute_object  =Attribute::find($attribute["id"]);

            if ($attribute_object->type != "range")
                $toHash_attrs[]=$attribute ;

            RentalRequestAttribute::create([
                "attribute_id"=>$attribute["id"],
                "request_id"=>$create->id,
                "value"=>is_array($attribute["value"])?json_encode($attribute["value"]):$attribute["value"],
                "payload"=>json_encode($attribute_object),

            ]);
        }

        $create->hash = md5(json_encode([$request->category_id , $toHash_attrs])) ;
        $create->save();



        if ($request->media)
            foreach ($request->media as $row) {
                $filename = $row->store('/', 'public');
                RentalRequestsMedia::create([
                    'request_id' => $create->id,
                    'image' => $filename,
                ]);
            }

        $create->getMatchingRentalRequestsAndNotify(true);

        return $this->sendResponse(new RentalRequestResource(RentalRequest::find($create->id)),null);
    }
    public function editRentalRequest(Request $request ,$request_id){

        if ($request->type ==RentalRequest::$TYPE_SEARCH) {

            $this->validate(
                $request,
                [
                    'name' => "required",

                    'category_id' => "required",
                    'type' => "required",
                    'search_price_from' => "required|numeric",
                    'search_price_to' => "required|numeric",
                ],[],[

                    "search_price_from" => \Lang::get("lang.search_price_from") ,
                    "search_price_to" => \Lang::get("lang.search_price_to") ,
                ]
            );

        }else if ($request->type ==RentalRequest::$TYPE_OFFER){
            $this->validate(
                $request,
                [

                    'name' => "required",
                    'category_id' => "required",
                    'type' => "required",
                    'offer_price' => "required|numeric",
                ],[],[

                    "offer_price" => \Lang::get("lang.offer_price") ,
                ]
            );
        }else{
            $this->validate(
                $request,
                [
                    'category_id' => "required",
                    'type' => "required",
                ]
            );
        }


        $currentUser = auth('customers')->user();
        $request_request =  RentalRequest::where(function ($query){

            $currentUser = auth('customers')->user();
            $currentStore = auth('store')->user();

            if ($currentUser)
                $query->where("customer_id",$currentUser->id);

            if ($currentStore)
                $query->where("store_id",$currentStore->id);


        })->where("id",$request_id)->first();

        $currentStore = auth('store')->user();



        $request_request->update([
            "name"=>$request->name,
            //  "description"=>$request->description,
            "store_category_id"=>$request->category_id,
            "customer_id"=>$currentUser?$currentUser->id:null,
            "store_id"=>$currentStore?$currentStore->id:null,

            "offer_price"=>($request->type=="sell" ||$request->type =="search")? $request->offer_price :0,
            "search_price_from"=>($request->type=="buy"||$request->type =="search") ? $request->search_price_from:0,
            "search_price_to"=>($request->type=="buy" ||$request->type =="search")? $request->search_price_to:0,
            "type"=>$request->type,

            "country_id"=>optional(Governorate::find($request->governorate_id))->country_id,
            "governorate_id"=>$request->governorate_id,
            "city_id"=>$request->city_id,
            "region_id"=>$request->region_id,

            "rental_period"=>$request->rental_period,
            "currency_id"=>$request->currency_id,

            //  "price"=>$request->price,
            //   "hash"=>md5(json_encode([$request->category_id , $request->attributes_list]))
        ]);

        RentalRequestAttribute::where("request_id" , $request_id)->delete();
        $toHash_attrs = [];
        foreach ($request->attributes_list as $attribute){
            $attribute_object  =Attribute::find($attribute["id"]);
            if ($attribute_object->type != "range")
                $toHash_attrs[]=$attribute ;

            RentalRequestAttribute::create([
                "attribute_id"=>$attribute["id"],
                "request_id"=>$request_request->id,
                "value"=>is_array($attribute["value"])?json_encode($attribute["value"]):$attribute["value"],
                "payload"=>json_encode($attribute_object),

            ]);
        }



        $request_request->hash = md5(json_encode([$request->category_id , $toHash_attrs])) ;
        $request_request->save();



        if ($request->media)
            foreach ($request->media as $row) {
                $filename = $row->store('/', 'public');
                RentalRequestsMedia::create([
                    'request_id' => $request_request->id,
                    'image' => $filename,
                ]);
            }



        //

        $request_request->getMatchingRentalRequestsAndNotify(true);


        return $this->sendResponse(new RentalRequestResource(RentalRequest::find($request_request->id)),null);
    }

    public function rentalRequestDelete(Request $request ,$request_id ){

        $rentalRequest = RentalRequest::where(function ($query){

            $currentUser = auth('customers')->user();
            $currentStore = auth('store')->user();

            if ($currentUser)
                $query->where("customer_id",$currentUser->id);

            if ($currentStore)
                $query->where("store_id",$currentStore->id);


        })->where("id",$request_id)->first();

        if(is_null($rentalRequest) ){
            return $this->sendError('Rental Request not found',404);
        }elseif ($rentalRequest->customer_id != $this->CURRENT_USER->id  && $rentalRequest->store_id != $this->CURRENT_USER->id ){
            return $this->sendError('You do not have permission to delete this  item',401 );
        }

        $rentalRequest->delete();

        return $this->sendResponse(null, \Lang::get("lang.deleted_successfully"));

    }
    public function rentalRequestImageDelete(Request $request)
    {
        $this->validate(
            $request,
            [
                'request_id' => "required|exists:rental_requests,id",
                'image_id' => "required",

            ]
        );

        $request_request_media = RentalRequestsMedia::where("id", $request->image_id)
            ->where("request_id", $request->request_id)
            ->first();

        if (is_null($request_request_media)) {
            return $this->sendError(\Lang::get("lang.invalid_order"), 422);
        }
        $request_request_media->delete();
        return $this->sendResponse(null, \Lang::get("lang.deleted_successfully"));
    }
    public function myRentalRequestsList(Request $request){

        if($request->type){
            $request_requests = RentalRequest::where("type",$request->type)->where(function ($query){

                $currentUser = auth('customers')->user();
                $currentStore = auth('store')->user();

                if ($currentUser)
                    $query->where("customer_id",$currentUser->id);

                if ($currentStore)
                    $query->where("store_id",$currentStore->id);


            })
                ->orderBy("id","desc")->paginate(10);
        }else
            $request_requests = RentalRequest::where("customer_id",$this->CURRENT_USER->id)->orderBy("id","desc")->paginate(10);




        // $result = ($request_requests);

        $result = [
            'requests'=>RentalRequestResource::collection($request_requests),
            "pagination"=> [
                "i_total_objects"=>$request_requests->count(),
                "i_items_on_page"=> $request_requests->count(),
                "i_per_pages"=>$request_requests->perPage(),
                "i_current_page"=>$request_requests->currentPage() ,
                "i_total_pages"=> $request_requests->total()
            ]];

        return $this->sendResponse($result,null);

    }

    public function myRentalRequestDetails(Request $request,$rental_request_id){
        $rental_request = RentalRequest::find($rental_request_id);
        if ($rental_request)
            return $this->sendResponse(new RentalRequestResource($rental_request),null);
        else
            return $this->sendError(\Lang::get("lang.invalid_order"));

    }


  public function bop_send_to_page_Ads(Request $request){

        // $this->validate($request,['address_id'=>'required']);
        // $status = $this->checkProductStatusIsAvailable();

        // if ($status['status']>0){
        //     return $this->sendError($status['message'],422);
        // }

        // $address = Address::where('id',$request->address_id)
        //     ->where('customer_id',$this->CURRENT_USER->id)->first();
        // if(is_null($address)){
        //     return $this->sendError(\Lang::get("invalid_address"),422);
        // }

        // $delivery_cost = 0 ;
        // $store= null;




        // $carts= CartApi::has('product')
        //     ->where('customer_id',$this->CURRENT_USER->id)
        //     ->get();

        // $total = 0;
        // foreach ($carts as $cart){

        //     $product= Product::find($cart->product_id);
        //     $variation= Variation::find($cart->variation_id);
        //     $rate = getRateBaseOnProductType($product->product_type);
        //     //dd(round($variation->price_after_offer));
        //     if ($variation){

        //         if($variation->price_after_offer){
        //             $total += getPriceForUser(($variation->price_after_offer) * $cart->qty , $product->currency );;

        //         }else{
        //             $total += getPriceForUser(($variation->display_selling_price) *$cart->qty, $product->currency );
        //         }

        //     }else{

        //         if($product->is_offer){
        //             $total += getPriceForUser(($product->new_price) * $cart->qty, $product->currency );
        //         }else{
        //             $total += getPriceForUser(($product->price) *$cart->qty, $product->currency );
        //         }

        //     }
        //     $store = $product->store;
        // }

        // $coupon_code = Coupon::where('coupon_code',$request->coupon_code)->isActive()->first();

        // if($request->coupon_code){
        //     if(is_null($coupon_code)){
        //         return $this->sendError('الكابون الذي ادخلته غير موجود ',422);
        //     }
        //     if ($coupon_code->min_value && $coupon_code->max_value ){

        //         if (!($coupon_code->min_value < $total && $total <= $coupon_code->max_value)){
        //             $msg = " لا يمكن تنفيذ الكابون يجب ان يكون مجموع  الطلبية اقل من $coupon_code->max_value  واكثر من $coupon_code->min_value   ";
        //             return $this->sendError($msg,422);
        //         }
        //     }
        // }


        // $calculateCoupon=0;
        // $sub_total=$total;
        // $coupon_value=0;
        // $total = 0;
        // if($coupon_code){
        //     $calculateCoupon = $this->calculateCoupon($coupon_code,$sub_total);
        //     $coupon_value=$calculateCoupon['couponAmount'];
        //     $coupon_code['couponAmount']=$coupon_value;
        //     $total = numberFormat($calculateCoupon['total'],2);
        // }
        // if($coupon_code){
        //     $coupon_object = [
        //         'id'=>$coupon_code->id,
        //         'coupon_value'=>$coupon_code->coupon_value,
        //         'coupon_type'=>$coupon_code->coupon_type,
        //         'couponAmount'=>numberFormat($coupon_value,2),
        //     ];
        // }
        // else{

        //     $coupon_object=null;
        // }

        // if($coupon_object){

        //     if($total < 0 ){
        //         return $this->sendError('لا يمكن تطبيق الكابون لان قيمته أعلى من قيمة الطلب',422);
        //     }
        // }


        // $delivery = new Delivery();


        // $delivery_cost =     $delivery->get_delivery_costForUserCurrency($address,$store);


        // $total = ($total?getRound($total):getRound($sub_total)) + $delivery_cost ;



        // $setting = Setting::first();

        $view  =  view("bop",
            [
                "callback" =>route('bop_callback_Ads'),
                "total" =>$request->amount,
                "sub_total" =>0,
                "delivery_cost" =>0,
                "coupon" =>"",
                'currency'=> new CurrencyResource(getCurrentUserCurrencyUSD()),

                "extra"=>[
                    "customer_id"=>$this->CURRENT_USER->id,
                    "address_id"=>"test" ,
                    "time_stamp" => microtime(true)
                ],
            ]
        )->render() ;

          $res["web"] =  $view ;
           return $view ;

        // if ($request->version == "2") {
        //     $res["online_payment"] = true;
        //     if (strtolower($this->CURRENT_USER->device_type) == "ios") {
        //         if (!$setting->pay_ios_orders) {

        //             $res["online_payment"] = false;

        //         }
        //     } else {

        //         if (!$setting->pay_android_orders) {

        //             $res["online_payment"] = false;
        //         }

        //     }


        //     $res["web"] = $res["online_payment"] ? $view : "";

        //     if (!$res["online_payment"]){
        //         //save for cash on delivery
        //         $this->saveAfterPaymentComeFromBop($this->CURRENT_USER->id, $address->id);

        //     }

        //     return $this->sendResponse($res, \Lang::get("lang.order_sent_successfully"));
        // }else{

        //     return $view ;

        // }

    }

       public function bop_callback_Ads(Request $request){

        file_put_contents('payment_log_file.log',print_r($request->all(),true).'\n',FILE_APPEND);
        $data = \request()->all();
       // dd($data);


        $order = base64_decode($data['OrderID']);
        $order_data = json_decode($order);

        $ResponseCode = intval($data['ResponseCode']);
        $ReasonCode = intval($data['ReasonCode']);
        $PaymentStatus = $data['ReasonCodeDesc'];


        $payment = Payment::create([
            "customer_id"=>$order_data->customer_id,
            "payment_status"=>$PaymentStatus,
            "payload"=>json_encode($data)
        ]);


        if ( $ResponseCode ===1 && $ReasonCode ===1 ){


            $this->saveAfterPaymentComeFromBop($order_data->customer_id, $order_data->address_id,true);
            return  redirect()->route('checkout.success');

        }

        return  redirect()->route('checkout.fail',["message"=>isset($data['ReasonCodeDesc'])?$data['ReasonCodeDesc']:""]); //$this->sendError("error",406,$data);

    }

 public function MsgNotification(Request $request){

// $UserDetails;
$MyDetailsCustomer = Customer::whereIn('id', [$this->CURRENT_USER->id])->get();
$MyDetailsStore = Store::whereIn('id', [$this->CURRENT_USER->id])->get();




// if($request->store_id !=null)
// {
//     $UserDetails = Store::whereIn('id', [$request->store_id])->get();

// }
// else
// {
//     $UserDetails = Customer::whereIn('id', [$request->customer_id])->get();

// }
$fcmTokens;

if($request->store_id !=null){
 $fcmTokens = Store::whereIn('id', [$request->store_id])->whereNotNull('fcm_token')->pluck('fcm_token')->toArray();
}
else
{
     $fcmTokens = Customer::whereIn('id', [$request->customer_id])->whereNotNull('fcm_token')->pluck('fcm_token')->toArray();

}


                    $factory = (new Factory())->withServiceAccount( base_path(env('FIREBASE_CREDENTIALS')));

                    $messaging = $factory->createMessaging();

                  $UserSender;
                if($MyDetailsCustomer !=null){
                    $UserSender = $MyDetailsCustomer->first()->username; }
                    else
                    {
                          $UserSender = $MyDetailsStore->first()->name;
                    }
                $notification = FirebaseNotification::create("New Message From ".$UserSender, $request->body);


// $notification = FirebaseNotification::create()
//     ->withTitle("New Message From $UserSender")
//     ->withBody($request->body);
          foreach ( $customers1 as $customer){

              $data=[
                  'title' =>"New Message From ".$UserSender,
                  'body' => $request->body,
                  'id'=>null,
                  'type'=>'general_notification',
                  'link'=>''
              ];

            //   fcmNotification($customer,$data);
                  \Notification::send($customer,
                  new \App\Notifications\GeneralNotification(
                     $storeDetails->first()->name.' : '.$request->title,
                      'general_notification',
                      $data
                  ));
          }



           $message = CloudMessage::new()->withNotification($notification);

            // Send notification to all FCM tokens in one statement
            $batchSize = 150; // Adjust as needed

            // Split FCM tokens into batches and send notifications
            $chunks = array_chunk($fcmTokens, $batchSize);
            foreach ($chunks as $chunk) {
                try {
                    $messaging->sendMulticast($message, $chunk);
                    // echo "Notification sent successfully to batch of FCM tokens\n";
                } catch (\Throwable $e) {
                    // Handle errors
                    // echo "Error sending notification to batch of FCM tokens. Error: ".$e->getMessage()."\n";
                }
            }

       return $this->sendResponse($customers, $fcmTokens);
        //  $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');

}




}
