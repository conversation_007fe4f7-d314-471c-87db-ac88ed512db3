<?php

namespace App\Http\Controllers\Api;

use App\Action\Statistics;
use App\Http\Controllers\Controller;
use App\Http\Resources\Api\CommentResource;
use App\Http\Resources\CategoryFullResource;
use App\Http\Resources\CategoryResource;
use App\Http\Resources\ColorResource;
use App\Http\Resources\CustomerResource;
use App\Http\Resources\GeneralResource;
use App\Http\Resources\JobRequestResource;
use App\Http\Resources\MainCategoryResource;
use App\Http\Resources\MainColorResource;
use App\Http\Resources\PostResource;
use App\Http\Resources\ProductRequestResource;
use App\Http\Resources\RentalRequestResource;
use App\Http\Resources\ReviewResource;
use App\Http\Resources\SizeResource;
use App\Http\Resources\StoreOfferResource;
use App\Http\Resources\StoreResource;
use App\Http\Resources\SystemDrawResource;
use App\Models\Brand;

use App\Models\Category;
use App\Models\Color;
use App\Models\Comment;
use App\Models\Customer;
use App\Models\Favorite;
use App\Models\GallaryBanner;
use App\Models\JobRequest;
use App\Models\JobType;
use App\Models\Like;
use App\Models\Maincolor;
use App\Models\Offer;
use App\Models\Post;
use App\Models\Product;
use App\Models\ProductRequest;
use App\Models\RentalRequest;
use App\Models\Reviews;
use App\Models\Size;
use App\Models\Slider;
use App\Models\Store;
use App\Models\SubCategory;
use App\Models\SystemDraw;
use App\Models\Variation;
use App\Services\SendNotification;
use Illuminate\Http\Request;
use App\Http\Controllers\BaseControllerApi;
use App\Http\Resources\ProductResource;
use App\Http\Resources\BrandResource;
use App\Http\Resources\ProductHomeResource;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Lang;
use function PHPUnit\Framework\isEmpty;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;


class HomeApiController extends BaseControllerApi
{


    /*    public function index(Request $request){


        $sliders = Slider::orderBy('order','asc')->where('is_main',true)->get();
        $categories= Category::isActive()->isMain()->orderBy('created_at','desc')->get();
        $brands = Brand::isActive()->orderBy('order','asc')->limit(6)->get();
        $offers = Product::isAllActive()->offer()->orderBy('created_at','desc')->limit(10)->get();
        $news = Product::isAllActive()->new()->orderBy('created_at','desc')->limit(10)->get();
        $discount = Product::isAllActive()->discount()->orderBy('created_at','desc')->limit(10)->get();
        $category_products=[];
        $banners =GallaryBanner::isActive()->get();
        $category_products[]=[
            'id'=>'offer',
            'name'=>'مرحبا عروض',
            'products'=>ProductHomeResource::collection($offers),
        ];
        $category_products[]=[
            'id'=>'new',
            'name'=>'مرحبا جديد',
            'products'=>ProductHomeResource::collection($news),
        ];
        $category_products[]=[
            'id'=>'discount',
            'name'=>'أفضل الخصومات',
            'products'=>ProductHomeResource::collection($discount),
        ];
        $categories_banner = Category::isActive()->isMain()->where('is_main',true)->orderBy('created_at','desc')->get()->take(6);

        foreach ($categories_banner as $category){
            $category_products[]=[
                'id'=>$category->id,
                'name'=>$category->name,
                'products'=>ProductHomeResource::collection($category->products()->orderBy('created_at','desc')->paginate(8)),

            ];
        }
        $categoryWithProductAndAdvertisement= $this->addAdvertisementToProduct($category_products,$banners);
        $result = [
          'sliders'=>  $sliders,
          'categories'=>CategoryResource::collection($categories),
          'brands'=>  $brands,
          'category_products'=>  $categoryWithProductAndAdvertisement,

        ];


        return $this->sendResponse($result,'');
    }*/

    public function randomVideos(Request $request)
    {
        // Retrieve the randomVideoSeed from the session or create a new one if it's the first page or the seed doesn't exist.
        $randomVideoSeed = $request->session()->get("randomVideoSeed");

        if (!$randomVideoSeed || !isset($request->page) || $request->page == 1) {
            $randomVideoSeed = random_int(PHP_INT_MIN, PHP_INT_MAX);
            $request->session()->put('randomVideoSeed', $randomVideoSeed);
        }

        // Create the query for fetching posts
        $postsQuery = Post::with(['store', 'customer'])
            ->isActive()
            ->whereNotNull("media_thumbnail");

        // Apply specific filters based on the request type
        if ($request->type == "reels") {
            $postsQuery->where(function ($query) {
                $query->whereHas("store")->orWhereHas("customer");
            })->IsReels();
        } else {
            $postsQuery->IsNotHotSale()->IsNotReels();
        }

        // Order by the random seed for randomness
        $postsQuery->orderByRaw("RAND({$randomVideoSeed})");

        // Paginate the results
        $posts = $postsQuery->paginate(5);

        // Prepare the result array with pagination information
        $result = [
            'posts' => PostResource::collection($posts),
            'pagination' => [
                'i_total_objects' => $posts->total(),
                'i_items_on_page' => $posts->count(),
                'i_per_pages' => $posts->perPage(),
                'i_current_page' => $posts->currentPage(),
                'i_total_pages' => $posts->lastPage(),
            ]
        ];

        // Return the response
        return $this->sendResponse($result, '');
    }



    public function main(Request $request)
    {
        $customer_id = $this->CURRENT_USER->id;
        $page = $request->page ?? 1;
        $cacheKey = 'main_page_' . $page;
        $context = $this;

        // // $data= $this->mainContent($request, $customer_id);
        $data = Cache::remember($cacheKey, 5, function () use ($customer_id, $request, $context) {

            $result = HomeApiController::mainContent($request, $customer_id, $context);
            return json_encode($result); // Convert to JSON

        });
       
    
        $data = json_decode($data, true);
        return $this->sendResponse($data, '');
        // return $this->sendResponse(null, '');

    }
    public static function mainContent(Request $request, $customer_id, $context) {
        $stores = [];
        $system = null;
        $last_draw = null;
        $test_draw = null;
        $recommended_page_length = 15;
        $recommended_videos_page_length = 20;
        $posts_page_length = 30;

        if ($request->page == 1) {
            $oneHourAgo = now()->subHour();
            $stores = Store::isActive()
                ->inRandomOrder()->limit($recommended_page_length)->get();
            //        $system = Cache::remember('system_draw', 3600, function () {
            //            return SystemDraw::isSystemDraw()->isActive()->orderBy('date', 'desc')->first();
            //        });

            $system = SystemDraw::isSystemDraw()->isActive()->orderBy('date', 'desc')->first();

            if (!$system)
                $last_draw = SystemDraw::isSystemDraw()->canBeViewed()->where("is_end", true)->orderBy('date', 'desc')->where('date', '>=', $oneHourAgo)->first();
            else
                $last_draw = null;
        }

        if (!isset($request->page) || $request->page == 1) {
            session(['postsSeed' => random_int(PHP_INT_MIN, PHP_INT_MAX)]);
            session([
                'randomVideoSeed' => random_int(PHP_INT_MIN, PHP_INT_MAX),
                "videos_page" => 1,
                'productsRequestsSeed' => random_int(PHP_INT_MIN, PHP_INT_MAX),
                "productsRequests_page" => 1,
                "jobRequests_page" => 1,
                "rentalRequests_page" => 1,
                "customers_page" => 1
            ]);
        }

        $posts = $context->getPosts($request, $posts_page_length);
        $outPosts = PostResource::collection($posts);

        $postsMain = $context->getPostsMain();
        $outPostsMain = PostResource::collection($postsMain);

        if (!$outPosts->isEmpty() && ($request->type == "withReels")) {
            $outPosts = $context->bindAdditionalDataToPosts($request, $posts, $outPosts);
        }

        $result = [
            "is_active" => $system || $last_draw ? true : false,
            "draws" => new SystemDrawResource($system),
            "test_draw" => new SystemDrawResource($test_draw),
            "last_draw" => new SystemDrawResource($last_draw),
            "stores" => StoreResource::collection($stores),
            'posts' => $outPosts,
            'postMain' => $outPostsMain,
            "pagination" => [
                "i_total_objects" => $posts->count(),
                "i_items_on_page" => $posts->count(),
                "i_per_pages" => $posts->perPage(),
                "i_current_page" => $posts->currentPage(),
                "i_total_pages" => $posts->total()
            ]
        ];
        return $result;

    }
    //private function getPosts(Request $request, $posts_page_length) {
    //    return Post::where(function ($query) {
    //        $query->isActive()
    //            ->IsNotHotSale()
    //            ->where(function ($query) {
    //                $query->whereHas("store", function ($query) {
    //                    $query->isActive();
    //                })
    //                ->orWhereHas("customer");
    //            })
    //            ->where(function ($query) {
    //                $query->whereNull('type'); // Exclude rows where type is "reels"
    //            });
    //    })
    //    ->orderByNewestRandomly($request->session()->get("postsSeed"))
    //    ->paginate($posts_page_length);
    //}

    private function getPosts(Request $request, $posts_page_length)
    {
        $now = \Carbon\Carbon::now();
        $twoWeeksAgo = $now->subWeek();

        return Post::select('*', DB::raw("
            CASE
                WHEN created_at >= '$twoWeeksAgo' THEN 1
                ELSE 2
            END AS priority
        "))
            ->orderBy('priority')
            ->where(function ($query) {
                $query->isActive()                    // Active posts only
                    ->IsNotHotSale()                  // Exclude hot sale posts
                    ->where(function ($query) {
                        $query->whereHas("store", function ($query) {
                            $query->isActive();       // Stores must be active
                            $query->where('name', '!=', 'Premium Post'); // exclude premium post from regular feed
                        })
                            ->orWhereHas("customer");     // Either store or customer is required
                    })
                    ->where(function ($query) {
                        $query->whereNull('type');    // Exclude posts where type is "reels"
                    });
            })       
            ->inRandomOrder()  // Use inRandomOrder to randomize the posts
            ->paginate($posts_page_length);  // Paginate the results
    }


    //private function getPostsMain() {
    //    return Post::isNotReels()
    //        ->where(function ($query) {
    //            $query->isActive()->IsNotHotSale()->whereHas("store", function ($query) {
    //                $query->isActive()->where('is_Main', true);
    //            });
    //        })
    //        ->orderBy('created_at', 'desc')
    //        ->take(5)
    //        ->get();
    //}

    private function getPostsMain()
    {
        return Post::isNotReels()                 // Exclude reels
            ->where(function ($query) {
                $query->isActive()                // Active posts
                    ->IsNotHotSale()              // Exclude hot sale posts
                    ->whereHas("store", function ($query) {
                        $query->isActive()->where('is_Main', true);  // Only stores where `is_Main` is true
                    });
            })
            ->inRandomOrder()   // Randomize the top posts
            ->take(5)           // Limit to 5 posts
            ->get();
    }


    private function bindAdditionalDataToPosts(Request $request, $posts, $outPosts)
    {
        $reelsRandomVideos = $this->getReelsRandomVideos($request, 5);
        $randomRentalRequests = $this->getRandomRentalRequests($request, 5);
        $randomVideos = $this->getRandomVideos($request, 5);
        $randomCustomers = $this->getRandomCustomers($request, $this->CURRENT_USER->id, 5);
        $randomProductsRequests = $this->getRandomProductsRequests($request, 5);
        $randomJobRequests = $this->getRandomJobRequests($request, 5);
        $randomProducts = $this->getProductsRandomPage($request);
        $randomStores = $this->getRandomStores($request, 5); // New store method

        return $this->bindVideosToPosts(
            PostResource::collection($posts),
            PostResource::collection($randomVideos),
            PostResource::collection($reelsRandomVideos),
            ProductRequestResource::collection($randomProductsRequests),
            JobRequestResource::collection($randomJobRequests),
            RentalRequestResource::collection($randomRentalRequests),
            CustomerResource::collection($randomCustomers),
            ProductHomeResource::collection($randomProducts),
            StoreResource::collection($randomStores) // Add store collection
        );
    }

    private function bindVideosToPosts(
        $Postscollection,
        $VideosCollectionCollection,
        $ReelsVideosCollectionCollection,
        $ProductsRequestsCollection,
        $JobRequestsCollection,
        $RentalRequestsCollection,
        $CustomersCollection,
        $ProductsCollection,
        $StoresCollection // Add stores collection
    ) {
        $out = [];

        foreach ($Postscollection as $index => $post) {
            $out[] = [
                "type" => "post",
                "post" => $post
            ];

            if (($index + 1) % 3 == 0) {
                $position = ($index + 1) / 3;

                if ($position % 7 == 1 && !$ReelsVideosCollectionCollection->isEmpty()) {
                    if (!in_array('reelsv2', array_column($out, 'type'))) {
                        $out[] = [
                            "type" => "reelsv2",
                            "reels" => $ReelsVideosCollectionCollection
                        ];
                    }   
                } elseif ($position % 7 == 0 && !$RentalRequestsCollection->isEmpty()) {
                    $out[] = [
                        "type" => "rental_requests",
                        "rental_requests" => $RentalRequestsCollection
                    ];
                } elseif ($position % 7 == 3 && !$CustomersCollection->isEmpty()) {
                    if (!in_array('customers', array_column($out, 'type'))) {
                        $out[] = [
                            "type" => "customers",
                            "customers" => $CustomersCollection
                        ];
                    }    
                } elseif ($position % 7 == 4 && !$VideosCollectionCollection->isEmpty()) {
                    continue;
                    $out[] = [
                        "type" => "reels",
                        "reels" => $VideosCollectionCollection
                    ];
                } elseif ($position % 7 == 5 && !$ProductsRequestsCollection->isEmpty()) {
                    $out[] = [
                        "type" => "products_requests",
                        "products_requests" => $ProductsRequestsCollection
                    ];
                } elseif ($position % 7 == 6 && !$JobRequestsCollection->isEmpty()) {
                    $out[] = [
                        "type" => "job_requests",
                        "job_requests" => $JobRequestsCollection
                    ];
                } elseif ($position % 7 == 2 && !$ProductsCollection->isEmpty()) {

                    if (!in_array('random_products', array_column($out, 'type'))) {
                        $out[] = [
                            "type" => "random_products",
                            "random_products" => $CustomersCollection
                        ];
                    }  
                 
                } elseif ($position % 7 == 2 && !$StoresCollection->isEmpty()) {
                    $out[] = [
                        "type" => "store",
                        "store" => $StoresCollection
                    ];
                }
            }
        }

        return $out;
    }

    private function getRandomStores($request, $recommended_page_length)
    {
        // Increment the stores page stored in the session
        $stores_page = $request->session()->get("stores_page", 0) + 1;
        session(["stores_page" => $stores_page]);

        // Query to get random stores with a limit
        return Store::query()
            ->inRandomOrder() // Randomize the selection
            ->limit($recommended_page_length) // Limit the result set to the recommended page length
            ->get();
    }



    private function getProductsRandomPage($request, $recommended_page_length = 8)
    {
        // Get the current page from the session and increment it
        $products_page = $request->session()->get("products_page", 0) + 1;
        session(["products_page" => $products_page]);

        // Query for random products
        return Product::isAllActive()
            ->inRandomOrder()
            ->limit($recommended_page_length)
            ->get();
    }


    private function getRandomCustomers($request, $customer_id, $recommended_page_length)
    {
        $customers_page = $request->session()->get("customers_page") + 1;
        session(["customers_page" => $customers_page]);

        return Customer::query()
            ->whereDoesntHave("follower", function ($query) use ($customer_id) {
                $query->where("customer_id", $customer_id);
            })
            ->inRandomOrder()
            ->limit($recommended_page_length)
            ->get();
    }

    private function getRandomVideos($request, $recommended_videos_page_length)
    {
        $videos_page = $request->session()->get("videos_page") + 1;
        session(["videos_page" => $videos_page]);

        return Post::has("store")
            ->isNotReels()
            ->whereNotNull("media_thumbnail")
            ->inRandomOrder()
            ->whereDate('created_at', '>=', now()->subMonths(1))
            ->limit($recommended_videos_page_length)
            ->get();
    }

    private function getRandomProductsRequests($request, $recommended_page_length)
    {
        $productsRequests_page = $request->session()->get("productsRequests_page") + 1;
        session(["productsRequests_page" => $productsRequests_page]);

        return ProductRequest::where("type", "sell")
            ->where(function ($query) {
                $query->has("store")->orHas("customer");
            })
            ->inRandomOrder()
            ->limit($recommended_page_length)
            ->get();
    }

    private function getReelsRandomVideos($request, $recommended_videos_page_length)
    {
        $videos_page = $request->session()->get("reels_videos_page") + 1;
        session(["reels_videos_page" => $videos_page]);

        return Post::where(function ($q) {
            $q->whereHas("store")->orWhereHas("customer");
        })
            ->whereNotNull("media_thumbnail")
            ->isReels()
            ->inRandomOrder()
            ->whereDate('created_at', '>=', now()->subMonths(1))
            ->limit($recommended_videos_page_length)
            ->get();
    }

    private function getRandomRentalRequests($request, $recommended_page_length)
    {
        $rental_page = $request->session()->get("rental_requests_page") + 1;
        session(["rental_requests_page" => $rental_page]);

        return RentalRequest::where("type", RentalRequest::$TYPE_OFFER)
            ->where(function ($query) {
                $query->has("store")->orHas("customer");
            })
            ->inRandomOrder()
            ->limit($recommended_page_length)
            ->get();
    }

    private function getRandomJobRequests($request, $recommended_page_length)
    {
        $jobRequests_page = $request->session()->get("jobRequests_page") + 1;
        session(["jobRequests_page" => $jobRequests_page]);

        return JobRequest::where("type", "offer")
            ->where(function ($query) {
                $currentUser = auth('customers')->user();
                $currentStore = auth('store')->user();

                if ($currentUser) {
                    $query->where("customer_id", "!=", $currentUser->id)->orWhereNull("customer_id");
                }

                if ($currentStore) {
                    $query->where("store_id", "!=", $currentStore->id)->orWhereNull("store_id");
                }
            })
            ->inRandomOrder()
            ->limit($recommended_page_length)
            ->get();
    }


    public function main_draw(Request $request)
    {

        $system_draw = SystemDraw::isSystemDraw()->isActive()->orderBy('date', 'desc')->first();

        $result = $system_draw ? new SystemDrawResource($system_draw) : null;
        return $this->sendResponse($result, '');
    }


    public function test_draw(Request $request)
    {

        $test_draw = SystemDraw::where("store_id", 414)->isActive()->orderBy('date', 'desc')->first();

        $result = $test_draw ? new SystemDrawResource($test_draw) : null;
        return $this->sendResponse($result, '');
    }

    public function last_draw(Request $request)
    {

        $last_draw = SystemDraw::isSystemDraw()->isEndAndLast()->orderBy('date', 'desc')->first();

        $result = $last_draw ? new SystemDrawResource($last_draw) : null;

        return $this->sendResponse($result, '');
    }



    public function getAllNew(Request $request)
    {
        $data = Product::with('relatedModel')->isAllActive()->new()->orderBy('created_at', 'desc')->paginate(12);
        $result = [
            'product' => ProductHomeResource::collection($data),
            "pagination" => [
                "i_total_objects" => $data->total(),
                "i_items_on_page" => $data->count(),
                "i_per_pages" => $data->perPage(),
                "i_current_page" => $data->currentPage(),
                "i_total_pages" => $data->lastPage()
            ]
        ];
        return $this->sendResponse($result, '');
    }

    // Similarly optimize the rest of these functions...

    public function getAllOffer(Request $request)
    {

        $system = SystemDraw::isActive()->first();

        if ($request->is_black_friday) {

            $data = [
                "draws" => [
                    "is_active" => $system ? true : false,
                    "draws" => new SystemDrawResource($system)
                ],
                "black_friday" => [
                    "is_active" => true,
                    "image_url" => null,
                ],
                'offers' => $this->getBlackFridayOffers(),
                "pagination" => [
                    "i_total_objects" => 1,
                    "i_items_on_page" => 1,
                    "i_per_pages" => 1,
                    "i_current_page" => 1,
                    "i_total_pages" => 1
                ]

            ];
            return $this->sendResponse($data, '');
        } else {
            $offers = Offer::isActive()->paginate(10);
        }


        $data = [
            "draws" => [
                "is_active" => $system ? true : false,
                "draws" => new SystemDrawResource($system)
            ],
            "black_friday" => [
                "is_active" => $offer ? true : false,
                "image_url" => $offer ? $offer->image_url : null,
            ],
            'offers' => StoreOfferResource::collection($offers),
            "pagination" => [
                "i_total_objects" => $offers->count(),
                "i_items_on_page" => $offers->count(),
                "i_per_pages" => $offers->perPage(),
                "i_current_page" => $offers->currentPage(),
                "i_total_pages" => $offers->total()
            ]

        ];
        return $this->sendResponse($data, '');
    }
    public function getOfferDetails(Request $request)
    {


        if ($request->offer_id == 0) {
            if ($request->store_id)
                $products = Product::where("is_hotsale", true)->where("store_id", $request->store_id)->paginate(10);
            else
                $products = Product::where("is_hotsale", true)->paginate(10);
        } else {

            $offer = Offer::findOrFail($request->offer_id);

            if ($request->store_id)
                $products = Product::where("offer_id", $request->offer_id)->where("store_id", $request->store_id)->paginate(10);
            else
                $products = Product::where("offer_id", $request->offer_id)->paginate(10);
        }

        $data = [
            'products' => ProductHomeResource::collection($products),
            "pagination" => [
                "i_total_objects" => $products->count(),
                "i_items_on_page" => $products->count(),
                "i_per_pages" => $products->perPage(),
                "i_current_page" => $products->currentPage(),
                "i_total_pages" => $products->total()
            ]

        ];
        return $this->sendResponse($data, '');
    }

    public function getHotSaleBlackFriday()
    {
        $posts = Post::isActive()->IsHotSale()->orderBy('created_at', 'desc')/*->inRandomOrder("id")*/
            ->paginate(20);

        $postsHotSale = Post::where("posts.type", "=", "hot_sale")
            ->orderBy('posts.created_at', 'desc')
            ->joinSub(function ($query) {
                $query->select('store_id', DB::raw('MAX(created_at) as latest_created_at'))
                    ->from('black_friday_requests')
                    ->groupBy('store_id');
            }, 'latest_black_friday_requests', function ($join) {
                $join->on('posts.store_id', '=', 'latest_black_friday_requests.store_id');
            })
            ->join('black_friday_requests', function ($join) {
                $join->on('posts.store_id', '=', 'black_friday_requests.store_id')
                    ->on('black_friday_requests.created_at', '=', 'latest_black_friday_requests.latest_created_at');
            })
            ->join('stores', 'stores.id', '=', 'posts.store_id') // Join with stores table
            ->where('posts.status', "=", "published")
            ->select('posts.*', 'black_friday_requests.*', 'stores.*')
            ->paginate(12);;



        $data = [
            'posts' => PostResource::collection($posts),
            'postAll' => $postsHotSale,
            "pagination" => [
                "i_total_objects" => $posts->count(),
                "i_items_on_page" => $posts->count(),
                "i_per_pages" => $posts->perPage(),
                "i_current_page" => $posts->currentPage(),
                "i_total_pages" => $posts->total()
            ]

        ];
        return $this->sendResponse($data, '');
    }

    public function getAllDiscount(Request $request)
    {
        $data = Product::isAllActive()->offer()->orderBy('created_at', 'desc')->paginate(12);
        $result = [
            'product' => ProductHomeResource::collection($data),
            "pagination" => [
                "i_total_objects" => $data->count(),
                "i_items_on_page" => $data->count(),
                "i_per_pages" => $data->perPage(),
                "i_current_page" => $data->currentPage(),
                "i_total_pages" => $data->total()
            ]

        ];
        return $this->sendResponse($result, '');
    }


    public function category_list(Request $request)
    {
        $categories = collect();
        if ($request->level == "main") {
            $categories = Category::isMain()->isActive()->orderByRaw("JSON_EXTRACT(name, '$." . App::getLocale() . "') asc")->get();
        } elseif ($request->level == "sub" && $request->parent_id) {
            $categories = SubCategory::isActive()->where('main_category_id', $request->parent_id)->orderByRaw("JSON_EXTRACT(name, '$." . App::getLocale() . "') asc")->get();
        } elseif ($request->level == "sub_sub" && $request->parent_id) {
            $categories = SubCategory::isActive()->where('parent_id', $request->parent_id)->orderByRaw("JSON_EXTRACT(name, '$." . App::getLocale() . "') asc")->get();
        }

        $result = CategoryFullResource::collection($categories);
        return $this->sendResponse($result ?? [], '');
    }

    public function category(Request $request)
    {
        $this->validate($request, [
            'store_id' => 'required|exists:stores,id',
        ]);

        $categories = Category::isActive()
            ->WithStoreProducts($request->store_id)
            ->isMain()
            ->orderBy('created_at', 'desc');

        if ($request->has('page')) {
            $categories = $categories->paginate(12);
            $result = [
                'category' => MainCategoryResource::collection($categories),
                "pagination" => [
                    "i_total_objects" => $categories->total(),
                    "i_items_on_page" => $categories->count(),
                    "i_per_pages" => $categories->perPage(),
                    "i_current_page" => $categories->currentPage(),
                    "i_total_pages" => $categories->lastPage()
                ]
            ];
        } else {
            $categories = $categories->get();
            $result = [
                'category' => MainCategoryResource::collection($categories),
            ];
        }

        return $this->sendResponse($result, '');
    }

    public function sub_category(Request $request)
    {
        $categories = SubCategory::isActive();

        if ($request->main_category_id) {
            $categories = $categories->where('main_category_id', $request->main_category_id);
        } elseif ($request->parent_id) {
            $categories = $categories->where('parent_id', $request->parent_id);
        }

        if ($request->store_id) {
            $store_id = $request->store_id;
            $categories->whereHas('sub_products', function ($query) use ($store_id) {
                $query->where("store_id", $store_id);
            });
        }

        $categories = $categories->orderBy('created_at', 'asc');
        if ($request->has('page')) {
            $categories = $categories->paginate(12);
        } else {
            $categories = $categories->get();
        }

        $result = [
            'category' => CategoryResource::collection($categories),
            "pagination" => $request->has('page') ? [
                "i_total_objects" => $categories->total(),
                "i_items_on_page" => $categories->count(),
                "i_per_pages" => $categories->perPage(),
                "i_current_page" => $categories->currentPage(),
                "i_total_pages" => $categories->lastPage()
            ] : null
        ];

        return $this->sendResponse($result, '');
    }


    public function systemDraw(Request $request)
    {
        $system = SystemDraw::isEndAndLast()->first();
        if ($request->draw_id) {
            $system = SystemDraw::find($request->draw_id);
        }

        return $this->sendResponse([
            "is_active" => $system ? true : false,
            "draws" => new SystemDrawResource($system)
        ], '');
    }

    //     public function getProducts(Request $request)
    // {
    //     // Validate inputs if necessary
    //     // $this->validate($request, ['category_id' => 'required']);

    //     // Initialize order_by with a default value if not provided
    //     if (!$request->has('order_by')) {
    //         $request->order_by = "new";
    //     }

    //     // Start building the query for fetching products
    //     $products = Product::isAllActive();

    //     // Handle single product fetch by product_id
    //     if ($request->product_id) {
    //         $product = $products->findOrFail($request->product_id);
    //         return $this->sendResponse(['product' => new ProductResource($product)], '');
    //     }

    //     // Apply category filter if provided
    //     if ($request->category_id) {
    //         $products->getByCategory($request->category_id);
    //     }

    //     // Apply store filter if provided
    //     if ($request->store_id) {
    //         $products->where('store_id', $request->store_id);
    //     }

    //     // Apply search by name if provided
    //     if ($request->search_name) {
    //         $searchTerm = $request->search_name;
    //         $products->where(function ($query) use ($searchTerm) {
    //             $query->where(DB::raw('title'), 'LIKE', '%' . $searchTerm . '%');
    //         });
    //     }
    //     // Apply type filters if provided
    //     if ($request->type == 'new') {
    //         $products->new();
    //     } elseif ($request->type == 'offer') {
    //         $products->offer();
    //     } elseif ($request->type == 'discount') {
    //         $products->discount();
    //     }

    //     // Apply brand filter if provided
    //     if ($request->brand_id) {
    //         $products->where('brand_id', $request->brand_id);
    //     }

    //     // If order_by is set and greater than 0, filter by store's category
    //     if ($request->has('order_by') && $request->order_by > 0) {
    //         $stores = Store::where('category_id', $request->order_by)->pluck('id');
    //         $products->whereIn('store_id', $stores);
    //     }

    //     // Randomize product results
    //     $products->inRandomOrder();

    //     // Paginate the results
    //     $products = $products->paginate(10);

    //     // Prepare the result
    //     $result = [
    //         'product' => ProductHomeResource::collection($products),
    //         "pagination" => [
    //             "i_total_objects" => $products->total(),
    //             "i_items_on_page" => $products->count(),
    //             "i_per_pages" => $products->perPage(),
    //             "i_current_page" => $products->currentPage(),
    //             "i_total_pages" => $products->lastPage()
    //         ]
    //     ];

    //     return $this->sendResponse($result, '');
    // }


    public function getProducts(Request $request)
    {
        // Initialize the query
  
        if ($request->product_id) {
            $productId = $request->product_id;
            $cacheKey = 'main_page_products_' . $productId;

            $data = Cache::remember($cacheKey, 300, function () use ($productId) {
                $product  = Product::isAllActive()->findOrFail($productId);
                $result = [
                    'product' => new ProductResource($product),
                ];
    
                return $result;
            });
            return $this->sendResponse($data, '');
        }
        $categoryId = $request->category_id ?? '';
        $storeId = $request->store_id ?? '';
        $searchName = $request->search_name ?? '';
        $type = $request->type ?? '';
        $brandId = $request->brand_id ?? '';
        $orderBy = $request->order_by ?? '';
        $page = $request->page ?? '';
        $cacheKey = "main_page_products_filter_category:$categoryId:storeId:$storeId:searchName:$searchName:type:$type:brandId:$brandId:orderBy:$orderBy:page:$page";
        $result = Cache::remember($cacheKey, 600, function () use ($request) {
            $products = Product::where('status', 1);
            $products = $products->inRandomOrder();
    
            if ($request->category_id) {
                $products->getByCategory($request->category_id);
            }
    
            if ($request->store_id) {
                $products->where('store_id', $request->store_id);
            }
    
            if ($request->search_name) {
                $products->onProductTitle($request->search_name);
            }
    
            if ($request->type == 'new') {
                $products->new();
            }
    
            if ($request->type == 'offer') {
                $products->offer();
            }
    
            if ($request->type == 'discount') {
                $products->discount();
            }
    
            if ($request->brand_id) {
                $products->where('brand_id', $request->brand_id);
            }
    
            // if ($request->has('order_by') && $request->order_by != 'new') {
            //     if ($request->order_by > 0) {
            //         $products->where('main_category_id', $request->order_by)
            //         ->orWhere('sub_category_id', $request->order_by);
            //         $products->inRandomOrder();
            //     }
            // }
            if ($request->has('order_by')) {
                if ($request->order_by > 0 && $request->order_by != 'new') {
                    // Retrieve stores by category ID
                    $stores = Store::where('category_id', $request->order_by)->get();
                    $storeIds = $stores->pluck('id');
        
                    // Apply the store filter and random order to the existing query
                    $products->whereIn('store_id', $storeIds)->inRandomOrder();
                }
            }
            // Paginate the results
            $products = $products->paginate(12);
    
            $result = [
                'product' => ProductHomeResource::collection($products),
                "pagination" => [
                    "i_total_objects" => $products->count(),
                    "i_items_on_page" => $products->count(),
                    "i_per_pages" => $products->perPage(),
                    "i_current_page" => $products->currentPage(),
                    "i_total_pages" => $products->total()
                ]
            ];
    
    
            return json_encode($result);
        });
        $result = json_decode($result, true);
        return $this->sendResponse($result, '');
    }


    public function getProductsRandom(Request $request)
    {
        if (!$request->has('order_by')) {
            $request->order_by = "new";
        }
        $products = Product::isAllActive();
        // Get 8 random products
        $products = $products->inRandomOrder()->limit(8)->get();

        $result = [
            'products' => ProductHomeResource::collection($products),
            'pagination' => [
                'i_total_objects' => $products->count(),
                'i_items_on_page' => $products->count(),
                'i_per_pages' => 8,
                'i_current_page' => 1,
                'i_total_pages' => 1
            ]
        ];

        return $this->sendResponse($result, '');
    }






    public function searchProducts(Request $request)
    {
        file_put_contents('search.log', print_r($request->all(), true) . '\n', FILE_APPEND);

        if (!$request->has('order_by')) {
            $request->order_by = "new";
        }

        $products = Product::isAllActive();

        if ($request->category_id) {
            $products->getByCategory($request->category_id);
        }

        if ($request->brand_id) {
            $products->whereIn('brand_id', $request->brand_id);
        }

        if ($request->price_from && $request->price_to) {
            $price_from = $request->price_from;
            $price_to = $request->price_to;
            $products->where(function ($query) use ($price_from, $price_to) {
                $query->whereBetween('price', [$price_from, $price_to])
                    ->orWhereBetween('new_price', [$price_from, $price_to]);
            });
        }

        if ($request->color_id) {
            $variation_id = Variation::whereIn('product_color_id', $request->color_id)
                ->pluck('product_id')->toArray();
            $products->whereIn('id', $variation_id);
        }

        if ($request->size_id || $request->size) {
            $size_ids = $request->size_id ?: $request->size;
            $sizes = Size::whereIn('id', $size_ids)->pluck('size_ar')->toArray();
            $variation_id = Variation::where(function ($query) use ($sizes) {
                foreach ($sizes as $size) {
                    $query->orWhere('size_en', 'like', '%' . $size . '%');
                }
            })->pluck('product_id')->toArray();
            $products->whereIn('id', $variation_id);
        }

        if ($request->store_id) {
            $products->where('store_id', $request->store_id);
        }

        if ($request->search_name) {
            $searchTerm = $request->search_name;
            $products->where(function ($query) use ($searchTerm) {
                $query->where(DB::raw('title COLLATE utf8_unicode_ci'), 'LIKE', '%' . $searchTerm . '%');
            });
        }

        if ($request->order_by == 'new') {
            $products->orderBy('created_at', 'desc');
        } elseif ($request->order_by == 'best_seller') {
            $products->orderBy('sell_number', 'desc');
        } elseif ($request->order_by == 'highest_price') {
            $products->orderByRaw('CASE WHEN new_price > 0 THEN new_price ELSE price END DESC');
        } elseif ($request->order_by == 'lowest_price') {
            $products->orderByRaw('CASE WHEN new_price > 0 THEN new_price ELSE price END ASC');
        } else {
            $products->inRandomOrder();
        }

        $products = $products->paginate(40);

        $result = [
            'product' => ProductHomeResource::collection($products),
            "pagination" => [
                "i_total_objects" => $products->total(),
                "i_items_on_page" => $products->count(),
                "i_per_pages" => $products->perPage(),
                "i_current_page" => $products->currentPage(),
                "i_total_pages" => $products->lastPage()
            ]
        ];

        return $this->sendResponse($result, '');
    }



    public function related_product(Request $request)
    {
        $this->validate($request, [
            'product_id' => 'required',
        ]);

        $products = Product::isAllActive();
        if ($request->product_id) {
            $product  = Product::FindOrFail($request->product_id);
            $products->where('main_category_id', $product->category_id)->where('id', '!=', $product->id);
            $products = $products->paginate(12);
            $result = [
                'product' => ProductHomeResource::collection($products),
                "pagination" => [
                    "i_total_objects" => $products->count(),
                    "i_items_on_page" => $products->count(),
                    "i_per_pages" => $products->perPage(),
                    "i_current_page" => $products->currentPage(),
                    "i_total_pages" => $products->total()
                ]

            ];
            return $this->sendResponse($result, '');
        }
    }


    public  function getCategorySizeOrColor(Request $request)
    {
        file_put_contents('getCategorySizeOrColor.log', print_r($request->all(), true) . '\n', FILE_APPEND);
        if ($request->category_id) {

            //CHECK category_id IS SUB OR SUB SUB
            $sub_objects = SubCategory::where('id', $request->category_id)->first();
            $sub = SubCategory::where('id', $request->category_id)->whereNull('parent_id')->pluck('id')->toArray();

            $brands_id = Product::where('category_id', $request->category_id)->pluck('brand_id')->toArray();
            $brands = Brand::whereIn('id', $brands_id)->get();

            if (!empty($sub)) {

                $sub_sub = SubCategory::whereIn('parent_id', $sub)->pluck('id')->toArray();
                $variations = Variation::whereIn('category_id', $sub_sub);
            } else {

                $variations = Variation::where('category_id', $request->category_id);
            }
            // return color array

            $colors_variations =  $variations->pluck('product_color_id')->toArray();

            //                $colors_ma=Color::whereIn('id',$colors_variations)->get()
            //                    ->unique('human_name')
            //                    ->pluck('name')
            //                    ->toArray();
            //$color_main_ids=Color::whereIn('id',$colors_variations)->get()->pluck('main_color_id')->toArray();
            $colors =  Maincolor::whereIn('id', $colors_variations)->get();
            //                $colors=Color::whereIn('id',$colors_variations)->get();
            // return size array

            $variations =  $variations->pluck('product_size_id')->toArray();

            $sizes = Size::whereIn('id', $variations)->get()->unique('size_ar');
            //

            $result = [
                'color' => MainColorResource::collection($colors),
                'size' => SizeResource::collection($sizes),
                'brands' => BrandResource::collection($brands),
            ];

            return $this->sendResponse($result, '');
        } else {
            $result = [
                'msg' => \Lang::get("lang.enter_category"),

            ];
            return $this->sendError($result, '404');
        }
    }

    public function colors()
    {
        $colors = \App\Models\Maincolor::all();

        return $this->sendResponse($colors, '');
    }

    public function add_reviews(Request $request)
    {
        $this->validate($request, [
            'product_id' => 'required',
            'rating' => 'required',
        ]);

        $review = Reviews::create([
            'review' => $request->review ?? '',
            'product_id' => $request->product_id,
            'rating' => $request->rating,
            'status' => 1,
            'customer_id' => $this->CURRENT_USER->id,
        ]);

        $data_product = Product::find($review->product_id);

        $review_data = new ProductResource($data_product);

        return $this->sendResponse(["product" => $review_data], '');
    }


    public function favorites(Request $request)
    {

        $data = Product::isAllActive()->isFavorite();
        if ($request->search) {
            $data->where('title', 'LIKE', '%' . $request->search . '%');
        }

        $data = $data->orderBy('created_at', 'desc')->paginate(12);
        $result = [
            'product' => ProductHomeResource::collection($data),
            "pagination" => [
                "i_total_objects" => $data->count(),
                "i_items_on_page" => $data->count(),
                "i_per_pages" => $data->perPage(),
                "i_current_page" => $data->currentPage(),
                "i_total_pages" => $data->total()
            ]

        ];
        return $this->sendResponse($result, '');
    }


    public function favoriteAddOrRemove(Request $request)
    {
        $this->validate($request, [
            'product_id' => 'required',
        ]);


        $findProduct = Favorite::where('customer_id', $this->CURRENT_USER->id)
            ->where('product_id', $request->product_id)
            ->first();
        if (is_null($findProduct)) {

            $create = Favorite::create([
                'product_id' => $request->product_id,
                'customer_id' => $this->CURRENT_USER->id
            ]);
            $message = \Lang::get("lang.added_to_favorite");
            $type = false;
        } else {
            $findProduct->delete();
            $type = true;
            $message = \Lang::get("lang.deleted_from_favorite");
        }

        $product = Product::isAllActive()->find($request->product_id);
        return $this->sendResponse(['product' => new ProductResource($product)], '');
    }


    public function brands(Request $request)
    {
        //        if($request->page ==2){
        //            $result= [
        //                'brand'=>[],
        //                "pagination"=> [
        //                    "i_total_objects"=>6,//$data->count(),
        //                    "i_items_on_page"=> 6,//$data->count(),
        //                    "i_per_pages"=>6,//$data->perPage(),
        //                    "i_current_page"=>1,//$data->currentPage() ,
        //                    "i_total_pages"=> 0,//$data->total()
        //                ]
        //
        //            ];
        //            return $this->sendResponse($result,'');
        //        }
        $data = Brand::isActive()->orderBy('order', 'asc')->paginate(10);
        $result = [
            'brand' => BrandResource::collection($data),
            "pagination" => [
                "i_total_objects" => $data->count(),
                "i_items_on_page" => $data->count(),
                "i_per_pages" => $data->perPage(),
                "i_current_page" => $data->currentPage(),
                "i_total_pages" => $data->total()
            ]

        ];
        return $this->sendResponse($result, '');
    }


    public function addAdvertisementToProduct($productResource, $advertisements)
    {
        //  $product_test =$product->currentPage()+ $product_top;


        $last = 1;
        $final_array = collect();

        for ($i = 0; $i < count($productResource); $i++) {

            if (($last % 1) == 0) {
                if (!$advertisements->isEmpty()) {
                    // $final_array->push($advertisements->random(1)[0]);
                    //                    $new_resource =$productResource[$i]->toArray();
                    $productResource[$i]['banner'] = $advertisements[$i];
                }
            }
            $final_array->push($productResource[$i]);
            $last++;
        }

        return $final_array;
    }

    public function getReviews(Request $request)
    {
        $this->validate($request, [
            'product_id' => 'required',
        ]);
        $data = Reviews::where('product_id', $request->product_id)->orderBy('created_at', 'desc')->paginate(12);
        $result = [
            'reviews' => ReviewResource::collection($data),
            "pagination" => [
                "i_total_objects" => $data->count(),
                "i_items_on_page" => $data->count(),
                "i_per_pages" => $data->perPage(),
                "i_current_page" => $data->currentPage(),
                "i_total_pages" => $data->total()
            ]

        ];
        return $this->sendResponse($result, '');
    }



    public function relatedProducts(Request $request)
    {

        $product = Product::findOrFail($request->product_id);

        $data = Product::isAllActive()
            ->where("id", "!=", $request->product_id)
            ->where("main_category_id", $product->main_category_id)
            ->inRandomOrder()->paginate(12);
        $result = [
            'product' => ProductHomeResource::collection($data),
            "pagination" => [
                "i_total_objects" => $data->count(),
                "i_items_on_page" => $data->count(),
                "i_per_pages" => $data->perPage(),
                "i_current_page" => $data->currentPage(),
                "i_total_pages" => $data->total()
            ]

        ];
        return $this->sendResponse($result, '');
    }
    public function searchStoreAndProduct(Request $request)
    {

        $stores = Store::query();
        $products = Product::query();
        $customers = Customer::query();

        $products->isAllActive();
        if ($request->search_query) {

            $stores->where('name', "LIKE", '%' . $request->search_query . "%");
            $stores = $stores->orderBy("created_at", "desc")->limit(3)->get();

            $customers->where('username', "LIKE", '%' . $request->search_query . "%");
            $customers = $customers->orderBy("created_at", "desc")->limit(3)->get();



            $products = $products->onProductTitle($request->search_query);

            //where('title',"LIKE",'%'.$request->search_query."%");
            $products = $products->orderBy("created_at", "desc")->limit(3)->get();

            $result = [
                'stores' => StoreResource::collection($stores),
                'product' => ProductResource::collection($products),
                'customers' => CustomerResource::collection($customers),

            ];

            return $this->sendResponse($result, '');
        }

        $stores = $stores->orderBy("created_at", "desc")->limit(5)->get();

        $customers = $customers->orderBy("created_at", "desc")->limit(5)->get();

        $products = $products->orderBy("created_at", "desc")->limit(5)->get();

        $result = [
            'stores' => StoreResource::collection($stores),
            'product' => ProductResource::collection($products),
            'customers' => CustomerResource::collection($customers),


        ];
        return $this->sendResponse($result, '');
    }



    public function searchCustomers(Request $request)
    {

        $randomCustomersSeed = $request->session()->get("randomCustomersSeed");


        if (!$randomCustomersSeed || !isset($request->page) ||  $request->page == 1) {

            session(['randomCustomersSeed' => random_int(PHP_INT_MIN, PHP_INT_MAX)]);
        }


        $data = Customer::query();

        if ($request->name) {

            $data->where(function ($query) use ($request) {

                $query->where('username', "LIKE", '%' . $request->name . "%");
            });
        }

        $data = $data->inRandomOrder($request->session()->get("randomCustomersSeed"))->paginate(12);

        $result = [
            'customers' => CustomerResource::collection($data),
            "pagination" => [
                "i_total_objects" => $data->count(),
                "i_items_on_page" => $data->count(),
                "i_per_pages" => $data->perPage(),
                "i_current_page" => $data->currentPage(),
                "i_total_pages" => $data->total()
            ]
        ];
        return $this->sendResponse($result, '');
    }


    public function getProductAndStoreOnOffer()
    {

        $products = Product::whereHas("blackFriday")->where("is_hotsale", true)->limit(6)->get();

        $stores = [];
        foreach ($products as $product) {

            $stores["store_" . $product->store_id][] = $product->id;
        }

        return $stores;
    }
    public function getBlackFridayOffers()
    {
        $finals = [];
        foreach ($this->getProductAndStoreOnOffer()  as $key => $main_offer) {
            $store = explode("_", $key);
            $offer = explode("_", $key);

            $store_id = $store[1];
            $store = Store::find($store_id);
            $product =  Product::find($main_offer);
            if ($store && $product) {
                $new_offer = [
                    "id" => 0,
                    "title" => "HotSale",
                    'store' => new  StoreResource($store),
                    "start_date" => "",
                    "end_date" => "",
                    "products" => ProductHomeResource::collection($product)
                ];

                $finals[] = $new_offer;
            }
        }
        return $finals;
    }

    public function fcm_token_test(Request $request)
    {


        $user = Customer::find($request->user_id);
        if ($request->user_type == "store") {
            $user = Store::find($request->user_id);
        }
        $data = [
            'title' => $request->title,
            'body' => $request->body,
            'object_id' => $request->id,
            'type' => $request->type,
            'link' => $request->link
        ];
        fcmNotification($user, $data, true);

        return [];
    }


    //    public function likeOrUnlikePost(Request $request){
    //
    //        $this->validate($request,[
    //            'post_id'=>'required',
    //        ]);
    //
    //
    //
    //        $post = Post::find($request->post_id);
    //        $like = Like::where("likeable_id",$request->post_id)
    //            ->where("likeable_type","App\Models\Post")
    //            ->where("customer_id",$this->CURRENT_USER->id)
    //            ->first();
    //
    //        if(is_null($post)){
    //            return $this->sendError('post not found',404);
    //        }
    //
    //
    //        if($like){
    //            $like->delete();
    //            return $this->sendResponse(["is_like"=>false,
    //            "likes_count"=>$post->like()->count(),
    //            "comments_count"=>$post->comments()->count()],\Lang::get("lang.deleted_from_favorite"));
    //        }
    //
    //        $like = new Like();
    //        $post->like()->save($like);
    //        $like->customer_id = $this->CURRENT_USER->id;
    //        $like->save();
    //
    //        return $this->sendResponse(["is_like"=>true,
    //            "likes_count"=>$post->like()->count(),
    //            "comments_count"=>$post->comments()->count()],\Lang::get("lang.added_to_favorite"));
    //    }

    public function likeOrUnlikePost(Request $request)
    {

        $this->validate($request, [
            'post_id' => 'required',
        ]);

        $post = Post::find($request->post_id);
        $like = Like::where("likeable_id", $request->post_id)
            ->where("likeable_type", "App\Models\Post")
            ->where("customer_id", $this->CURRENT_USER->id)
            ->first();

        if (is_null($post)) {
            return $this->sendError('post not found', 404);
        }

        if ($like) {
            $like->delete();
            // Decrease like_count
            $post->decrement('like_count');
            return $this->sendResponse([
                "is_like" => false,
                "likes_count" => $post->like_count,
                "comments_count" => $post->comments()->count()
            ], \Lang::get("lang.deleted_from_favorite"));
        }

        $like = new Like();
        $post->like()->save($like);
        $like->customer_id = $this->CURRENT_USER->id;
        $like->save();

        // Increase like_count
        $post->increment('like_count');

        return $this->sendResponse([
            "is_like" => true,
            "likes_count" => $post->like_count,
            "comments_count" => $post->comments()->count()
        ], \Lang::get("lang.added_to_favorite"));
    }



    public function addComment(Request $request)
    {


        $this->validate($request, [
            'post_id' => 'required',
            'comment_text' => 'required',
        ]);


        $post = Post::find($request->post_id);

        if (is_null($post)) {
            return $this->sendError('post not found', 404);
        }


        if ($post->store)
            SendNotification::sendCommentOnPost($post->store, $this->CURRENT_USER, $post);
        else if ($post->customer)
            SendNotification::sendCommentOnPost($post->customer, $this->CURRENT_USER, $post);


        $create = Comment::create([
            'customer_id' => $this->CURRENT_USER->id,
            "post_id" => $post->id,
            "comment_text" => $request->comment_text,
        ]);

        return $this->sendResponse(new \App\Http\Resources\CommentResource($create), \Lang::get("lang.added_successfully"));
    }


    public function comment(Request $request)
    {
        //        $this->validate($request,[
        //            'post_id'=>'required',
        //        ]);
        $post = null;
        if ($request->post_id) {
            $post = Post::find($request->post_id);
            if (is_null($post)) {
                return $this->sendError(\Lang::get("lang.not_found"), 422);
            }
        }


        $comments = Comment::where("post_id", $post->id)->whereNull("parent_id")->paginate(12);
        $result = [
            'comment' => \App\Http\Resources\CommentResource::collection($comments),
            "pagination" => [
                "i_total_objects" => $comments->count(),
                "i_items_on_page" => $comments->count(),
                "i_per_pages" => $comments->perPage(),
                "i_current_page" => $comments->currentPage(),
                "i_total_pages" => $comments->total()
            ]
        ];
        return $this->sendResponse($result, 'success');
    }

    public function deleteComment(Request $request)
    {
        $comment = Comment::find($request->comment_id);
        // dd($comment,$comment->post,$this->CURRENT_USER->id);

        if (is_null($comment)) {
            return $this->sendError('Comment not found', 404);
        } elseif ($comment->customer_id != $this->CURRENT_USER->id) {
            return $this->sendError('You do not have permission to delete this  Comment', 401);
        }

        Comment::where("parent_id", $comment->id)->delete();
        $comment->delete();

        return $this->sendResponse(null, \Lang::get("lang.deleted_successfully"));
    }







    public function job_types(Request $request)
    {


        if ($request->has('page')) {
            $job_types = JobType::isActive()
                ->orderByRaw("JSON_EXTRACT(name, '$." . App::getLocale() . "') asc")->paginate(12);
            $result = [
                'jobs_types' => GeneralResource::collection($job_types),
                "pagination" => [
                    "i_total_objects" => $job_types->count(),
                    "i_items_on_page" => $job_types->count(),
                    "i_per_pages" => $job_types->perPage(),
                    "i_current_page" => $job_types->currentPage(),
                    "i_total_pages" => $job_types->total()
                ]

            ];
        } else {
            $job_types = JobType::isActive()->orderBy('created_at', 'desc')->get();
            $result = [
                'jobs_types' => GeneralResource::collection($job_types),
            ];
        }


        return $this->sendResponse($result, '');
    }
}
