<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\AddressResource;
use App\Http\Resources\AreaResource;
use App\Http\Resources\BlackFridayRequestResource;
use App\Http\Resources\NotificationApiResource;
use App\Http\Resources\PostResource;
use App\Http\Resources\StoreResource;
use App\Models\Address;
use App\Models\BlackFridayRequest;
use App\Models\City;
use App\Models\Comment;
use App\Models\Faq;
use App\Models\Governorate;
use App\Models\Post;
use App\Models\PostMedia;
use App\Models\ProductImage;
use App\Models\ProductRequest;
use App\Models\Region;
use App\Models\Store;
use App\Models\StoreCategory;
use App\Services\SendNotification;
use App\Services\VideoEdit;
use Illuminate\Http\Request;
use App\Http\Controllers\BaseControllerApi;
use Illuminate\Support\Facades\Hash;

use Illuminate\Support\Facades\Storage;

use App\Models\Customer;
use App\Services\S3StorageService;

class ReelsApiController extends BaseControllerApi
{

    protected $storageService;

    public function __construct(S3StorageService $storageService)
    {
        parent::__construct();
        $this->storageService = $storageService;
    }
    public function index(Request $request)
    {
        $data = Post::where(function ($query) {
            $currentUser = auth('customers')->user();
            $currentStore = auth('store')->user();

            // Check if current user is authenticated
            if ($currentUser) {
                $query->where("customer_id", $currentUser->id);
            }

            // Check if current store is authenticated
            if ($currentStore) {
                $query->where("store_id", $currentStore->id);
            }
        })
            // Filter for posts of type REELS
            ->where('type', Post::REELS_TYPE)  // Ensure to fetch only REELS_TYPE posts
            ->IsReels() // Assuming this is a scope that further filters for reels
            ->orderBy("created_at", "desc")
            ->paginate(12);

        $result = [
            'posts' => PostResource::collection($data),
            "pagination" => [
                "i_total_objects" => $data->total(),   // Total number of objects in the database
                "i_items_on_page" => $data->count(),   // Number of items on the current page
                "i_per_pages" => $data->perPage(),      // Items per page
                "i_current_page" => $data->currentPage(), // Current page number
                "i_total_pages" => $data->lastPage(),   // Total pages available
            ]
        ];

        return $this->sendResponse($result, '');
    }



//    public function index(Request $request)
//    {
//
//        $data = Post::where(function ($query){
//
//            $currentUser = auth('customers')->user();
//            $currentStore = auth('store')->user();
//
//            if ($currentUser)
//                $query->where("customer_id",$currentUser->id);
//
//            if ($currentStore)
//                $query->where("store_id",$currentStore->id);
//
//
//        });
//
//
//        $data->IsReels();
//
//        $data = $data->orderBy("created_at", "desc")->paginate(12);
//
//
//
//        $result = [
//
//            'posts' => PostResource::collection($data),
//
//            "pagination" => [
//                "i_total_objects" => $data->count(),
//                "i_items_on_page" => $data->count(),
//                "i_per_pages" => $data->perPage(),
//                "i_current_page" => $data->currentPage(),
//                "i_total_pages" => $data->total()
//            ]
//
//        ];
//        return $this->sendResponse($result, '');
//    }

    public function store(Request $request)
    {
        $this->validate($request, [
            'description' => 'nullable|string',
            'video' => 'file|mimes:mp4,mov,wmv,mkv|max:102400', // Add other valid video types
            'media.*' => 'nullable|file|image', // Validate media files
        ]);

        $currentUser = auth('customers')->user();
        $currentStore = auth('store')->user();

        // Create Post
        $create = Post::create([
            "description" => $request->description,
            "status" => "published",
            "customer_id" => $currentUser ? $currentUser->id : null,
            "store_id" => $currentStore ? $currentStore->id : null,
            "type" => Post::REELS_TYPE, // Ensure this is set correctly
        ]);

        // Debugging: Log the created post data
        \Log::info('Post Created:', $create->toArray());

        if ($request->file("video")) {
            $filename = $request->file("video")->store('/videos', 'public');
            $create->last_media = $filename;
            $create->media = VideoEdit::compressVideo($filename);
            $videoFileContents = Storage::disk('public')->get($create->media);

            $ext = pathinfo($filename, PATHINFO_EXTENSION);
            $videoExtensions = ['mp4', 'mov', 'm4v', 'wmv', 'mkv'];
            $this->storageService->uploadFile($create->media, $videoFileContents);
            if (in_array($ext, $videoExtensions)) {
                $create->media_thumbnail = VideoEdit::generateVideoThumbnail($filename);
                $videoFileContents = Storage::disk('public')->get($create->media_thumbnail);

                $this->storageService->uploadFile($create->media_thumbnail, $videoFileContents);

            }
            

            $create->save(); // Save the changes after handling the video
        }

        // Handle other media uploads
        if ($request->media) {
            foreach ($request->media as $row) {
                $filename = $row->store('/', 'public');
                PostMedia::create([
                    'post_id' => $create->id,
                    'image' => $filename,
                ]);
            }
        }

        return $this->sendResponse(new PostResource(Post::find($create->id)), 'Post created successfully');
    }


//    public function store(Request $request)
//    {
//        $this->validate($request, [
//            'description' => 'nullable|string',
//            'video' => 'nullable|file|mimes:mp4,mov,wmv,mkv', // Add other valid video types
//            'media.*' => 'nullable|file|image', // Validate media files
//        ]);
//
//        $currentUser = auth('customers')->user();
//        $currentStore = auth('store')->user();
//
//        // Create Post
//        $postData = [
//            "description" => $request->description,
//            "status" => "published",
//            "customer_id" => $currentUser ? $currentUser->id : null,
//            "store_id" => $currentStore ? $currentStore->id : null,
//            "type" => Post::$REELS_TYPE, // Ensure this is set correctly
//        ];
//
//        // Create post and check if it was successful
//        $create = Post::create($postData);
//
//        if (!$create) {
//            \Log::error('Post creation failed: ' . json_encode($postData));
//            return $this->sendResponse(null, 'Post creation failed', 500);
//        }
//
//        // Handle video upload if exists
//        if ($request->file("video")) {
//            $filename = $request->file("video")->store('/', 'public');
//            $create->last_media = $filename;
//            $create->media = VideoEdit::compressVideo($filename);
//
//            // Check video extension
//            $ext = pathinfo($filename, PATHINFO_EXTENSION);
//            $videoExtensions = ['mp4', 'mov', 'mkv', 'avi'];
//
//            if (in_array($ext, $videoExtensions)) {
//                $create->media_thumbnail = VideoEdit::generateVideoThumbnail($filename);
//            }
//
//            $create->save(); // Ensure the post is saved with media data
//        }
//
//        // Handle other media uploads
//        if ($request->media) {
//            foreach ($request->media as $row) {
//                $filename = $row->store('/', 'public');
//                PostMedia::create([
//                    'post_id' => $create->id,
//                    'image' => $filename,
//                ]);
//            }
//        }
//
//        return $this->sendResponse(new PostResource(Post::find($create->id)), 'Post created successfully');
//    }





    public function update(Request $request)
    {
        $this->validate(
            $request,
            [
                "post_id" => "required|exists:posts,id",
            ]
        );


        $post = Post::where("id", $request->post_id)->where(function ($query){

            $currentUser = auth('customers')->user();
            $currentStore = auth('store')->user();

            if ($currentUser)
                $query->where("customer_id",$currentUser->id);

            if ($currentStore)
                $query->where("store_id",$currentStore->id);


        })->first();


        if (is_null($post)) {
            return $this->sendError( \Lang::get("lang.no_permissions"), 422);
        }
        $filename = $request->video ? $request->file("video")
            ->store('/videos', 'public') : $post->last_media;

        $media = null;
        if ($request->file("video")) {

            $ext = pathinfo($filename, PATHINFO_EXTENSION);
            $videoExtension = ['mp4', 'mov', 'mpg', 'mpeg', 'wmv', 'mkv', 'avi', 'ts', 'TS','m4v'];
            $media = VideoEdit::compressVideo($filename);
            $videoFileContents = Storage::disk('public')->get($media);
            $this->storageService->uploadFile($media, $videoFileContents);

            if (in_array($ext, $videoExtension)) {
                $post->media_thumbnail = VideoEdit::generateVideoThumbnail($filename);
                $videoFileContents = Storage::disk('public')->get($post->media_thumbnail);
                $this->storageService->uploadFile($post->media_thumbnail, $videoFileContents);
            }

        }

        $currentUser = auth('customers')->user();
        $currentStore = auth('store')->user();

        $post->update([
            "description" => $request->description,
            "media" => $media,
            "last_media" => ($filename),
            "status" => $request->status == "published" ? "published" : "draft",
            "customer_id"=>$currentUser?$currentUser->id:null,
            "store_id"=>$currentStore?$currentStore->id:null
        ]);

        //PostMedia::where('post_id',$post->id)->delete();

        foreach ($request->media as $row) {
            $filename = $row->store('/', 'public');
            PostMedia::create([
                'post_id' => $post->id,
                'image' => $filename,
            ]);
        }


        return $this->sendResponse(new PostResource(Post::find($post->id)), '');
    }

    public function delete(Request $request)
    {
        $this->validate(
            $request,
            [
                'id' => "required|exists:posts,id",
            ]
        );

        $post = Post::where("id", $request->id)->where(function ($query){

            $currentUser = auth('customers')->user();
            $currentStore = auth('store')->user();

            if ($currentUser)
                $query->where("customer_id",$currentUser->id);

            if ($currentStore)
                $query->where("store_id",$currentStore->id);


        })->first();
        if (is_null($post)) {
            return $this->sendError( \Lang::get("lang.no_permissions"), 422);
        }
        $post->delete();
        return $this->sendResponse(null,  \Lang::get("lang.deleted_successfully"));
    }

    public function imageDelete(Request $request)
    {
        $this->validate(
            $request,
            [
                'post_id' => "required|exists:posts,id",
                'image_id' => "required",

            ]
        );

        $product = PostMedia::where("id", $request->image_id)
            ->where("post_id", $request->post_id)
            ->first();

        if (is_null($product)) {
            return $this->sendError(\Lang::get("lang.deleted_successfully"), 422);
        }
        $product->delete();
        return $this->sendResponse(null, \Lang::get("lang.deleted_successfully"));
    }


        public function getPost($post_id)
        {

            $post = null;

            if ($post_id) {
                $post = Post::find($post_id);
                if (is_null($post)) {
                    return $this->sendError(\Lang::get("lang.not_found"), 422);
                }
            }

            return $this->sendResponse(new PostResource(Post::find($post->id)), '');


        }

        public function getPostComments(Request $request ,$post_id ){
//        $this->validate($request,[
//            'post_id'=>'required',
//        ]);
                $post =null;



                if ($post_id){

                    $post = Post::find($post_id);
                    if(is_null($post)){
                        return $this->sendError(' غير موجود',422);
                    }

                }


                $comments = Comment::where("post_id",$post->id)
                    ->whereNull("parent_id")
                    ->orderByRaw("id desc ")
                    ->paginate(12);



                $result = [
                    'comment' => \App\Http\Resources\CommentResource::collection($comments),
                    "pagination" => [
                        "i_total_objects" => $comments->count(),
                        "i_items_on_page" => $comments->count(),
                        "i_per_pages" => $comments->perPage(),
                        "i_current_page" => $comments->currentPage(),
                        "i_total_pages" => $comments->total()
                    ]
                ];
                return $this->sendResponse($result,'success');
        }

    public function replyComment(Request $request ,$post_id , $comment_id){
        $this->validate($request,[

            'comment_text'=>'required',
        ]);

        $comment = Comment::find($comment_id);


        $customer = Customer::find($comment->customer_id);
        //$post = Post::find($request->post_id);

        if(is_null($comment)){
            return $this->sendError('Comment not found',404);
        }


        SendNotification::sendReplyOnComment($this->CURRENT_USER,$customer,$post_id);

        $create = Comment::create([
            'customer_id'=>$comment->customer_id,
            "post_id"=>$comment->post_id,
            "parent_id"=>$comment->id,
            "comment_text"=>$request->comment_text,
        ]);

        return $this->sendResponse(new \App\Http\Resources\CommentResource($create),\Lang::get("lang.added_successfully"));
    }
    public function deleteComment(Request $request ,$post_id , $comment_id){

        $comment = Comment::find($comment_id);
       // dd($comment,$comment->post,$this->CURRENT_USER->id);

        if(is_null($comment) ){
            return $this->sendError( \Lang::get("lang.not_found"),404);
        }elseif ($comment->post->store_id != $this->CURRENT_USER->id ){
            return $this->sendError( \Lang::get("lang.no_permissions"),401 );
        }

        Comment::where("parent_id",$comment->id)->delete();
        $comment->delete();

        return $this->sendResponse(null,  \Lang::get("lang.deleted_successfully"));

    }

    public function getRecommended_stores(){

        session(['storesSeed' => random_int(PHP_INT_MIN,PHP_INT_MAX)]);

        $data = Store::isActive()->IsWholeSale();

        return  $data->inRandomOrder(session("storesSeed"))->paginate(5);

    }


}
