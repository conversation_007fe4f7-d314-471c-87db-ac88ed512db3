<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\BaseControllerApi;
use App\Http\Resources\CustomerResource;
use App\Models\Customer;
use App\Utils\AppleUtils;
use Illuminate\Http\Request;
use App\Mail\SocialLoginNotification;
use Illuminate\Support\Facades\Mail;

class SocialLoginAppleController extends BaseControllerApi
{
    //
    public function authenticate(Request $request)
    {
        // Validate input
        $request->validate([
            'authCode' => 'required|string',
        ]);
        try {
            $socialUser = AppleUtils::extractData($request->authCode);
            // Check if the user exists
            $user = Customer::where('provider_id', $socialUser['sub']) ->whereNotNull('provider_id')->first();

            if (!$user) {

                    // Handle missing email
                $email = $socialUser['email'] ?? 'user_' . uniqid() . '@wintoo.com';
                $username = explode('@', $email)[0]; // Split by '@' and take the first part

                // Create a new user if it doesn't exist
                $user = Customer::create([
                    'name' => $username,
                    'email' => $email,
                    'provider' => 'apple',
                    'provider_id' => $socialUser['sub']
                ]);

                Mail::to($user->email)->send(new SocialLoginNotification($user, 'apple'));
            }

            // Generate a token for the mobile app
            $token = $user->createToken('token')->plainTextToken;
            $user = new CustomerResource($user);
            $result = [
                'user' => $user,
                'token' => $token,
            ];
            return $this->sendResponse($result, '');
        } catch (\Exception $e) {
            return response()->json(['error' => 'Authentication failed', 'reason' => $e->getMessage()], 500);
        }
    }
}
