<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\BaseControllerApi;
use App\Http\Controllers\Controller;
use App\Http\Resources\CustomerResource;
use App\Models\Customer;
use Laravel\Socialite\Facades\Socialite;
use Illuminate\Http\Request;
use App\Mail\SocialLoginNotification;
use Illuminate\Support\Facades\Mail;

class SocialLoginController extends BaseControllerApi
{
    //
    public function authenticate(Request $request, $provider)
    {
        // Validate input
        $request->validate([
            'access_token' => 'required|string',
        ]);

        try {
            // Use the access token to fetch user details
            $socialUser = Socialite::driver($provider)->scopes(['email'])->userFromToken($request->access_token);

            // Check if the user exists
            $user = Customer::where('provider_id', $socialUser->getId())->first();

            if (!$user) {
                $user = Customer::where('email', $socialUser->getEmail())->first();
            }

            if (!$user) {
                // Handle missing email
                $email = $socialUser->getEmail() ?? 'user_' . uniqid() . '@wintoo.com';

                // Handle missing name
                $username = $socialUser->getName() ?? 'user_' . uniqid();

                // Create a new user if it doesn't exist
                $user = Customer::create([
                    'username' => $username,
                    'email' => $email,
                    'provider' => $provider,
                    'provider_id' => $socialUser->getId(),
                    'image' => $socialUser->getAvatar(),
                ]);
                Mail::to($user->email)->send(new SocialLoginNotification($user, $provider));
            }

            // Generate a token for the mobile app
            $token = $user->createToken('token')->plainTextToken;
            $user = new CustomerResource($user);
            $result = [
                'user' => $user,
                'token' => $token,
            ];
            return $this->sendResponse($result, '');
        } catch (\Exception $e) {
            return response()->json(['error' => 'Authentication failed'], 500);
        }
    }
    public function callback($provider)
    {
        try {
            $socialUser = Socialite::driver($provider)->stateless()->user();
    
            // Check if customer exists
            $customer = Customer::where('provider_id', $socialUser->getId())->first();
             if (!$customer) {
                $customer = Customer::where('email', $socialUser->getEmail())->first();
            }
 
            if (!$customer) {
                // Handle missing email
                $email = $socialUser->getEmail() ?? 'user_' . uniqid() . '@wintoo.com';

                // Handle missing name
                $username = $socialUser->getName() ?? 'user_' . uniqid();

                // Create a new customer if it doesn't exist
                $customer = Customer::create([
                    'username' => $username,
                    'email' => $email,
                    'provider' => $provider,
                    'provider_id' => $socialUser->getId(),
                    'image' => $socialUser->getAvatar(),          
                ]);
                Mail::to($customer->email)->send(new SocialLoginNotification($customer, $provider));
            }
    
            // Generate a token for API
            $token = $customer->createToken('token')->plainTextToken;
    
            $user = new CustomerResource($customer);
            $result = [
                'user' => $user,
                'token' => $token,
            ];
            return $this->sendResponse($result, '');
        } catch (\Exception $e) {
            print_r($e->getMessage());
            return response()->json(['error' => 'Unable to authenticate customer'], 500);
        }
    }
    public function redirect($provider)
{
    if (!in_array($provider, ['google', 'facebook', 'apple'])) {
        return response()->json(['error' => 'Invalid provider'], 400);
    }

    return Socialite::driver($provider)->stateless()->redirect();
}
}
