<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\StorejanProduct;
use Illuminate\Http\Request;

class StoreJanController extends Controller
{
    //

    protected $storeJanService;

    public function __construct(StorejanProduct $myService)
    {
        $this->storeJanService = $myService;
    }

    public function createOrUpdateProduct(Request $request){

        if(!empty($request->products)){
            $products = $request->all();
            $this->storeJanService->main($products);
        }
    }


    public function updateOrderStatus(Request $request){

        $this->storeJanService->updateOrderStatus($request->all());
    }
}
