<?php

namespace App\Http\Controllers;

use App\Events\ToastMessageEvent;
use Illuminate\Http\Request;

class ToastController extends Controller
{
    public function sendToast(Request $request)
    {
        // Validate request data
        $request->validate([
            'toast_title' => 'required|string|max:255',
            'toast_description' => 'required|string',
        ]);

        // Broadcast the toast message event
        event(new ToastMessageEvent($request->toast_title, $request->toast_description));

        return redirect()->back()->with('success', 'Toast message sent successfully!');
    }
}
