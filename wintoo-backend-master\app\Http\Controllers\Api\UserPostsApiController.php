<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\AddressResource;
use App\Http\Resources\AreaResource;
use App\Http\Resources\BlackFridayRequestResource;
use App\Http\Resources\CustomerResource;
use App\Http\Resources\NotificationApiResource;
use App\Http\Resources\PostResource;
use App\Http\Resources\StoreResource;
use App\Models\Address;
use App\Models\BlackFridayRequest;
use App\Models\City;
use App\Models\Comment;
use App\Models\CustomersBio;
use App\Models\Faq;
use App\Models\Governorate;
use App\Models\Like;
use App\Models\Post;
use App\Models\PostMedia;
use App\Models\ProductImage;
use App\Models\Region;
use App\Models\Store;
use App\Models\StoreCategory;
use App\Services\SendNotification;
use App\Services\VideoEdit;
use Illuminate\Http\Request;
use App\Http\Controllers\BaseControllerApi;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;


use App\Models\Customer;
use App\Services\S3StorageService;
use Illuminate\Support\Facades\Storage;

class UserPostsApiController extends BaseControllerApi
{
    protected $storageService;

    public function __construct(S3StorageService $storageService)
    {
        parent::__construct();
        $this->storageService = $storageService;
    }

    public function index(Request $request)
    {



        $data = Post::isNotReels()->where('customer_id', $this->CURRENT_USER->id);



        if ($request->type == Post::$VIDEOS_TYPE)
            $data->whereNotNull("media_thumbnail");


        if ($request->type == Post::$IMAGES_TYPE)
            $data->whereNull("media_thumbnail")->whereHas("post_media");


        if ($request->search_name) {
            $data->where('description', "LIKE", "%" . $request->search_name . "%");
        }

        $data = $data->orderBy("created_at", "desc")->paginate(12);

        $result = [
            'posts' => PostResource::collection($data),

            "pagination" => [
                "i_total_objects" => $data->count(),
                "i_items_on_page" => $data->count(),
                "i_per_pages" => $data->perPage(),
                "i_current_page" => $data->currentPage(),
                "i_total_pages" => $data->total()
            ]

        ];

        return $this->sendResponse($result, '');
    }

    public function store(Request $request)
    {
        $filename = null;

        $create = Post::create([

            "description" => $request->description,
            // "status" => $request->status == "published" ? "published" : "draft",
            "status" =>"published",
            "customer_id" => $this->CURRENT_USER->id ,
            "type" => $request->type==Post::$HOT_SALE_TYPE?Post::$HOT_SALE_TYPE:null,
            // "StartHotsale" => isset($request->StartHotsale) ? ($request->type==Post::$HOT_SALE_TYPE?$request->StartHotsale:null) : null,
            // "EndHotsale" => isset($request->EndHotsale) ?($request->type==Post::$HOT_SALE_TYPE?$request->EndHotsale:null):null,



        ]);

        if ($request->file("video")) {

            $filename = $request->file("video")->store('/videos', 'public');
          //  $create->media = $filename;
            $create->media = VideoEdit::compressVideo($filename);
            $videoFileContents = Storage::disk('public')->get($create->media);
            $this->storageService->uploadFile($create->media, $videoFileContents);

            $ext = pathinfo($filename, PATHINFO_EXTENSION);
            $videoExtension = ['mp4', 'mov', 'mpg', 'mpeg', 'wmv', 'mkv', 'avi', 'ts', 'TS','m4v'];

            if (in_array($ext, $videoExtension)) {
                $create->media_thumbnail = VideoEdit::generateVideoThumbnail($create->media);
                $videoFileContents = Storage::disk('public')->get($create->media_thumbnail);
                $this->storageService->uploadFile($create->media_thumbnail, $videoFileContents);
            }

            $create->save();
        }

        if ($request->media)
            foreach ($request->media as $row) {
                $filename = $row->store('/', 'public');
                PostMedia::create([
                    'post_id' => $create->id,
                    'image' => $filename,
                ]);
            }


        SendNotification::sendPostPublishedNotification($this->CURRENT_USER , $create);

        return $this->sendResponse(new PostResource(Post::find($create->id)), '');
    }



    public function update(Request $request)
    {
        $this->validate(
            $request,
            [
                "post_id" => "required|exists:posts,id",
                'description' => 'required',
            ]
        );
        $post = Post::where("id", $request->post_id)->where("customer_id", $this->CURRENT_USER->id)->first();
        if (is_null($post)) {
            return $this->sendError( \Lang::get("lang.no_permissions"), 422);
        }
        $filename = $request->video ? $request->file("video")
            ->store('/', 'public') : $post->video;


        if ($request->file("video")) {

            $ext = pathinfo($filename, PATHINFO_EXTENSION);
            $videoExtension = ['mp4', 'mov', 'mpg', 'mpeg', 'wmv', 'mkv', 'avi', 'ts', 'TS','m4v'];

            if (in_array($ext, $videoExtension)) {
                $post->media_thumbnail = VideoEdit::generateVideoThumbnail($filename);
            }

        }


        $post->update([
            "description" => $request->description,
            "media" => $filename,
            "status" => "published" ,
            "customer_id" => $this->CURRENT_USER->id
        ]);

        //PostMedia::where('post_id',$post->id)->delete();

        foreach ($request->media as $row) {
            $filename = $row->store('/', 'public');
            PostMedia::create([
                'post_id' => $post->id,
                'image' => $filename,
            ]);
        }

        SendNotification::sendPostUpdatedNotification($this->CURRENT_USER , $post);

        return $this->sendResponse(new PostResource(Post::find($post->id)), '');
    }

//    public function delete(Request $request)
//    {
//        $this->validate(
//            $request,
//            [
//                'id' => "required|exists:posts,id",
//            ]
//        );
//
//        $post = Post::where("id", $request->id)->where("customer_id", $this->CURRENT_USER->id)->first();
//        if (is_null($post)) {
//            return $this->sendError( \Lang::get("lang.no_permissions"), 422);
//        }
//        $post->delete();
//        return $this->sendResponse(null,  \Lang::get("lang.deleted_successfully"));
//    }


//    public function delete(Request $request)
//    {
//        // Validate the incoming request
//        $validatedData = $request->validate([
//            'id' => 'required|exists:posts,id',
//        ]);
//
//        // Find the post based on the given ID and the current user's ID
//        $post = Post::where('id', $validatedData['id'])
//            ->where('customer_id', $this->CURRENT_USER->id)
//            ->first();
//
//        // Check if the post exists and belongs to the current user
//        if (!$post) {
//            return $this->sendError(\Lang::get('lang.no_permissions'), 403); // Use 403 for forbidden
//        }
//
//        // Handle deletion of related data if necessary (e.g., comments, likes)
//        // Optionally, cascade deletes if set up in the database relations
//
//        try {
//            // Delete the post
//            $post->delete();
//            return $this->sendResponse(null, \Lang::get('lang.deleted_successfully'));
//        } catch (\Exception $e) {
//            // Log the error for debugging purposes
//            \Log::error('Error deleting post: ' . $e->getMessage());
//
//            // Return a response indicating the error
//            return $this->sendError(\Lang::get('lang.deletion_failed'), 500); // 500 for server error
//        }
//    }

    public function delete(Request $request)
    {
        $validatedData = $request->validate([
            'id' => 'required|exists:posts,id',
        ]);

        $post = Post::where('id', $validatedData['id'])
            ->where('customer_id', $this->CURRENT_USER->id)
            ->first();

        if (!$post) {
            return $this->sendError(\Lang::get('lang.no_permissions'), 403);
        }

        try {
            $post->delete();

            // Send response with a reload flag or message
            return $this->sendResponse(['reload' => true], \Lang::get('lang.deleted_successfully'));
        } catch (\Exception $e) {
            \Log::error('Error deleting post: ' . $e->getMessage());
            return $this->sendError(\Lang::get('lang.deletion_failed'), 500);
        }
    }


    public function imageDelete(Request $request)
    {
        $this->validate(
            $request,
            [
                'post_id' => "required|exists:posts,id",
                'image_id' => "required",

            ]
        );

        $product = PostMedia::where("id", $request->image_id)
            ->where("post_id", $request->post_id)
            ->first();

        if (is_null($product)) {
            return $this->sendError(\Lang::get("lang.deleted_successfully"), 422);
        }
        $product->delete();
        return $this->sendResponse(null, \Lang::get("lang.deleted_successfully"));
    }


        public function getPost($post_id)
        {

            $post = null;

            if ($post_id) {
                $post = Post::find($post_id);
                if (is_null($post)) {
                    return $this->sendError(\Lang::get("lang.not_found"), 422);
                }
            }

            return $this->sendResponse(new PostResource(Post::find($post->id)), '');


        }

//            public function GetMostPostlikes()
//        {
//
//            $post = Post::leftJoin('likes', 'posts.id', '=', 'likes.likeable_id')
//                ->select('posts.id', 'posts.description', 'posts.media', 'posts.status', 'posts.start_date', 'posts.end_date', DB::raw('COUNT(likes.likeable_id) as likes_count'))
//                ->where("status", "=", "published")
//                ->groupBy('posts.id', 'posts.description', 'posts.media', 'posts.status', 'posts.start_date', 'posts.end_date')  // Add all selected fields here
//                ->orderByDesc('likes_count')
//                ->take(10)
//                ->get();
//
//
////           foreach ($posts as $p) {
////     // Check if description is null
////     if ($p->description === null) {
////         // If description is null, assign an empty string
////         $p->description = "";
////     } else {
////         // Perform the emoji removal only if description is not null
////         $p->description = preg_replace('/[\x{1F600}-\x{1F64F}]/u', '', $p->description);
////     }
//// }
//  $result = [
//             'posts' => PostResource::collection($post),
//
//            ];
//            return $this->sendResponse($result, '');
//
//
//        }

    public function GetMostPostlikes()
    {
        try {
            // Eager load related models if needed (e.g., likes, comments, store)
            $posts = Post::with(['comments', 'store', 'customer'])
                ->select('id', 'description', 'media', 'status', 'start_date', 'end_date', 'like_count', 'store_id', 'customer_id')
                ->where("status", "published")
                ->orderByDesc('like_count')
                ->take(10)
                ->get();

            foreach ($posts as $post) {
                // Check for missing store and customer
                if (is_null($post->store)) {
                    \Log::info("Store is null for post ID: " . $post->id);
                }
                if (is_null($post->customer)) {
                    \Log::info("Customer is null for post ID: " . $post->id);
                }

                // Handle description
                if (is_null($post->description)) {
                    $post->description = "";
                } else {
                    $post->description = preg_replace('/[\x{1F600}-\x{1F64F}]/u', '', $post->description);
                }
            }

            // Construct the response
            $result = [
                'posts' => PostResource::collection($posts),
            ];

            return $this->sendResponse($result, '');

        } catch (\Exception $e) {
            \Log::error('Error in GetMostPostlikes: ' . $e->getMessage());
            return response()->json(['error' => 'An error occurred while fetching posts.'], 500);
        }
    }




    public function getPostComments(Request $request ,$post_id ){
//        $this->validate($request,[
//            'post_id'=>'required',
//        ]);
                $post =null;



                if ($post_id){
                    $post = Post::find($post_id);
                    if(is_null($post)){
                        return $this->sendError(' غير موجود',422);
                    }
                }


                $comments = Comment::where("post_id",$post->id)
                    ->whereNull("parent_id")
                    ->orderByRaw("id desc ")
                    ->paginate(12);



                $result = [
                    'comment' => \App\Http\Resources\CommentResource::collection($comments),
                    "pagination" => [
                        "i_total_objects" => $comments->count(),
                        "i_items_on_page" => $comments->count(),
                        "i_per_pages" => $comments->perPage(),
                        "i_current_page" => $comments->currentPage(),
                        "i_total_pages" => $comments->total()
                    ]
                ];
                return $this->sendResponse($result,'success');
        }


        public function getPostLikes(Request $request ,$post_id ){

                $post =null;

                if ($post_id){
                    $post = Post::find($post_id);
                    if(is_null($post)){
                        return $this->sendError(' غير موجود',422);
                    }
                }



         $customers =    Customer::whereIn("id" ,function ($q) use($post_id){
                $q->select('customer_id')
                    ->from( "likes" )
                    ->where( "likeable_id"  ,$post_id )
                    ->where( "likeable_type"  , "App\Models\Post" )
                //    ->whereNull("deleted_at");
                ;
            });

            if ($request->search_name){
                $customers->where('username',"LIKE",'%'.$request->search_name."%");
            }

            $customers =   $customers->paginate(10);




                $result = [
                    'customers' => \App\Http\Resources\CustomerResource::collection($customers),
                    "pagination" => [
                        "i_total_objects" => $customers->count(),
                        "i_items_on_page" => $customers->count(),
                        "i_per_pages" => $customers->perPage(),
                        "i_current_page" => $customers->currentPage(),
                        "i_total_pages" => $customers->total()
                    ]
                ];
                return $this->sendResponse($result,'success');
        }

    public function replyComment(Request $request ,$post_id , $comment_id){
        $this->validate($request,[

            'comment_text'=>'required',
        ]);

        $comment = Comment::find($comment_id);


        $customer = Customer::find($comment->customer_id);
        //$post = Post::find($request->post_id);

        if(is_null($comment)){
            return $this->sendError('Comment not found',404);
        }


        SendNotification::sendReplyOnComment($this->CURRENT_USER,$customer,$post_id);

        $create = Comment::create([
            'customer_id'=>$comment->customer_id,
            "post_id"=>$comment->post_id,
            "parent_id"=>$comment->id,
            "comment_text"=>$request->comment_text,
        ]);

        return $this->sendResponse(new \App\Http\Resources\CommentResource($create),\Lang::get("lang.added_successfully"));
    }
    public function deleteComment(Request $request ,$post_id , $comment_id){

        $comment = Comment::find($comment_id);
       // dd($comment,$comment->post,$this->CURRENT_USER->id);

        if(is_null($comment) ){
            return $this->sendError( \Lang::get("lang.not_found"),404);
        }elseif ($comment->post->customer_id != $this->CURRENT_USER->id ){
            return $this->sendError( \Lang::get("lang.no_permissions"),401 );
        }

        Comment::where("parent_id",$comment->id)->delete();
        $comment->delete();

        return $this->sendResponse(null,  \Lang::get("lang.deleted_successfully"));

    }


    public function getCustomerPosts(Request $request , $customer_id , $type = "normal")
    {

        $data = Post::isNotReels()->where('customer_id', $customer_id);

        if ($type  == Post::$VIDEOS_TYPE)
            $data->whereNotNull("media_thumbnail");


        if ($type  == Post::$IMAGES_TYPE)
            $data->whereNull("media_thumbnail")->whereHas("post_media");


        if ($request->search_name) {
            $data->where('description', "LIKE", "%" . $request->search_name . "%");
        }

        $data = $data->orderBy("created_at", "desc")->paginate(12);

        $result = [
            'posts' => PostResource::collection($data),

            "pagination" => [
                "i_total_objects" => $data->count(),
                "i_items_on_page" => $data->count(),
                "i_per_pages" => $data->perPage(),
                "i_current_page" => $data->currentPage(),
                "i_total_pages" => $data->total()
            ]

        ];

        return $this->sendResponse($result, '');
    }

    public function getCustomerData(Request $request , $customer_id )
    {
        $customer = Customer::findOrFail($customer_id);
        return $this->sendResponse(new CustomerResource($customer), '');
    }


    public function getCustomerAbout(Request $request , $customer_id )
    {

        $bios =    CustomersBio::where("customer_id",$customer_id)->where("current" , true)->get();

        $out = [
                "living" => null ,
                "work" => null ,
                "study" => null ,
        ] ;



        $bios->each(function ($item) use (&$out){
            $out[$item->type] = $item ;
        });

        return $this->sendResponse( $out,'');

    }





}
