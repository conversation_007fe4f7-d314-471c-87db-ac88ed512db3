<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Wallet;
use App\Models\Store;

class WalletController extends Controller
{
    public function index(Request $request)
    {
        
        
        $storeId = $request->store_id;
        $store = Store::findOrFail($storeId);
        $wallet = $store->wallet;
        
        return response()->json(['wallet' => $wallet]);
    }

    public function store(Request $request)
    {
        
        $request->validate([
            'store_id' => 'required|exists:stores,id',
            'full_name' => 'required',
            'address' => 'required',
            'bank_name' => 'required',
            'account_number' => 'required',
            'iban_number' => 'required',
        ]);

        $store = Store::findOrFail($request->store_id);

        // Check if the store already has a wallet
        if ($store->wallet) {
            return response()->json(['message' => 'This store already has a wallet'], 400);
        }

        $wallet = new Wallet([
            'store_id' => $request->store_id,
            'full_name' => $request->full_name,
            'address' => $request->address,
            'bank_name' => $request->bank_name,
            'account_number' => $request->account_number,
            'iban_number' => $request->iban_number,
        ]);

        $store->wallet()->save($wallet);

        return response()->json(['message' => 'Wallet created successfully', 'wallet' => $wallet], 201);
    }

    public function update(Request $request)
    {
        $request->validate([
            'full_name' => 'sometimes|required',
            'address' => 'sometimes|required',
            'bank_name' => 'sometimes|required',
            'account_number' => 'sometimes|required',
            'iban_number' => 'sometimes|required',
        ]);

        $wallet = Wallet::findOrFail($request->id);

        $wallet->update($request->only('full_name', 'address', 'bank_name', 'account_number', 'iban_number'));

        return response()->json(['message' => 'Wallet updated successfully', 'wallet' => $wallet]);
    }

    public function destroy($id)
    {
        $wallet = Wallet::findOrFail($id);
        $wallet->delete();

        return response()->json(['message' => 'Wallet deleted successfully']);
    }
}
