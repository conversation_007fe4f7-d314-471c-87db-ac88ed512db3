<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Ratchet\Client\WebSocket;

class WebSocketController extends Controller
{
    public function sendMessageToUsers(Request $request)
    {
        try {
            // Validate that the request has the message object and the required fields
            if (!$request->has('message')) {
                return response()->json(['error' => 'The request is missing the message object'], 400);
            }

            // Get title and description from the request
            $title = $request->input('message.title');
            $description = $request->input('message.description');

            // Check if title is provided
            if (!$title) {
                Log::error('Missing title in the message payload');
                return response()->json(['error' => 'Title is required'], 400);
            }

            // Check if description is provided
            if (!$description) {
                Log::error('Missing description in the message payload');
                return response()->json(['error' => 'Description is required'], 400);
            }

            // Log the received message payload
            Log::info('Received message payload', ['title' => $title, 'description' => $description]);

            // Create the message payload for WebSocket
            $messagePayload = json_encode([
                'title' => $title,
                'message' => $description
            ]);

            if (!$messagePayload) {
                Log::error('Failed to encode the message payload');
                return response()->json(['error' => 'Failed to create message payload'], 500);
            }

            // Log the message payload before sending
            Log::info('Sending WebSocket message:', ['payload' => $messagePayload]);

            // Send the message via WebSocket
            $this->sendToWebSocketServer($messagePayload);

            return response()->json(['success' => true]);

        } catch (\Exception $e) {
            // Log the exception with full details
            Log::error('Exception caught during message processing:', [
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Return a generic error response
            return response()->json(['error' => 'Failed to send message', 'details' => $e->getMessage()], 500);
        }
    }

    private function sendToWebSocketServer($message)
    {
        try {
            // Check if message is not empty
            if (empty($message)) {
                Log::error('Message payload is empty');
                throw new \Exception('Message payload is empty');
            }

            // Replace with your actual WebSocket server URL
            $wsServerUrl = 'ws://wintoo.me/socket';

            // Validate WebSocket URL
            if (filter_var($wsServerUrl, FILTER_VALIDATE_URL) === false) {
                Log::error('Invalid WebSocket URL');
                throw new \Exception('Invalid WebSocket URL: ' . $wsServerUrl);
            }

            // Log the WebSocket connection attempt
            Log::info('Connecting to WebSocket server:', ['url' => $wsServerUrl]);

            // Send the message to the WebSocket server
            \Ratchet\Client\connect($wsServerUrl)->then(function(WebSocket $conn) use ($message) {
                if ($conn) {
                    Log::info('Connected to WebSocket server');
                    $conn->send($message);
                    Log::info('Message sent to WebSocket server');
                    $conn->close();
                } else {
                    Log::error('Failed to establish WebSocket connection');
                    throw new \Exception('Failed to establish WebSocket connection');
                }
            }, function($e) {
                // Log the connection failure
                Log::error('Could not connect to WebSocket server:', ['error' => $e->getMessage()]);
            });
        } catch (\Exception $e) {
            // Log any unexpected exceptions during connection or sending
            Log::error('Error during WebSocket connection or message sending:', ['exception' => $e->getMessage()]);
            throw $e; // Re-throw the exception to handle it in the calling method
        }
    }
}
