<?php

namespace App\Http\Controllers;

use App\Models\City;
use App\Models\Governorate;
use App\Models\Product;
use App\Models\Store;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    public function index(Request $request){
        $customers = \App\Models\Customer::count();
        $orders = checkIfUserStore()  ?
            \App\Models\Order::where("store_id",auth()->user()->store->id)->count()
            : \App\Models\Order::count();
        $products = checkIfUserStore() ?\App\Models\Product::where("store_id",auth()->user()->store->id)->count() :\App\Models\Product::count();
        $categories = \App\Models\Category::count();
        $offers = \App\Models\Offer::where("store_id",auth()->user()->store->id)->count();
        $posts = \App\Models\Post::where("store_id",auth()->user()->store->id)->count();
        $systemDraws = \App\Models\SystemDraw::where("store_id",auth()->user()->store->id)->count();
        $Stores = \App\Models\Store::count();

        return view('dashboard.dashboard',compact(
            "customers",
            "orders",
             "products",
             "categories",
            "offers",
            "posts",
            "systemDraws",
            "Stores"

        ));
    }

    public function product(Request $request){

    }

    public function get_search_data(Request $request){
        $search=$request->q;
        if ($request->model == "product"){
            $data = Product::where("title","LIKE","%".$search."%")->paginate(10);
            return response()->json([
                'status'=>true,
                'options'=>$data,
                'message'=>'تم ارسال بنجاح الرجاء التحقق من الهاتف']);

        }
      //dd($request->all());
      $data =  City::query();
        if($search){
            $data->where('name', 'LIKE', '%'.$search.'%');
        }

        $data=$data->paginate(3);
        return response()->json([
            'status'=>true,
            'options'=>$data,
            'message'=>'تم ارسال بنجاح الرجاء التحقق من الهاتف']);

    }

    public function markAsRead($notification_id)
    {


        $notification =auth()->user()->notifications()->where('id', $notification_id)->first();

        if ($notification) {
            $notification->markAsRead();
            return response()->json([
                'status'=>true,
                'code'=>200,
                'data'=>$notification->data['actionURL'],

            ], 200);
        }



    }

    public function allMakeRead(){
        foreach (auth()->user()->unreadNotifications as $notification) {
            $notification->markAsRead();
        }

        return response()->json([
            'status'=>true,
            'code'=>200,


        ], 200);
    }
    public function download($model,$id){

        if ($model == "stores"){

            $store = Store::findOrFail($id);

            return view('print',compact(
                "store",
            ));
        }

    }


}
