<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Post;

class PreviewController extends Controller
{
    //
    public function preview($postId) {
        $post = Post::findOrFail($postId);
        $image = $post->media_thumbnail;  // assume full URL or a path you can transform to a URL

        if (empty($image) && $post->media()->count()) {
            // fallback to the first media record
            $image = $post->media()->first()->image; 
        }

        return view('preview.index', compact(
            "post",
            "image"
        ));
    }
}
