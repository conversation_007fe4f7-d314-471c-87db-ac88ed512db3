<?php

namespace App\Http\Controllers\Store;

use App\Http\Controllers\Controller;
use App\Http\Resources\AddressResource;
use App\Http\Resources\AreaResource;
use App\Http\Resources\BlackFridayRequestResource;
use App\Http\Resources\NotificationApiResource;
use App\Http\Resources\PostResource;
use App\Http\Resources\StoreResource;
use App\Models\Address;
use App\Models\BlackFridayRequest;
use App\Models\City;
use Carbon\Carbon;
use App\Models\Comment;
use App\Models\Faq;
use App\Models\Governorate;
use App\Models\Post;
use App\Models\PostMedia;
use App\Models\ProductImage;
use App\Models\Region;
use App\Models\Store;
use App\Models\StoreCategory;
use App\Services\SendNotification;
use App\Services\VideoEdit;
use Illuminate\Http\Request;
use App\Http\Controllers\BaseControllerApi;
use Illuminate\Support\Facades\Hash;


use App\Models\Customer;
use App\Services\S3StorageService;
use Illuminate\Support\Facades\Storage;

class StoreAdsApiController extends BaseControllerApi
{

//    public function index(Request $request)
//    {
//
//        $data = Post::isNotReels()->where('store_id', $this->CURRENT_USER->id);
//
//        if ($request->type == Post::$HOT_SALE_TYPE ){
//            $data->IsHotSale();
//        }else{
//            $data->IsNotHotSale();
//        }
//
//        if ($request->search_name) {
//            $data->where('description', "LIKE", "%" . $request->search_name . "%");
//        }
//
//
//        $data = $data->orderBy("created_at", "desc")->paginate(12);
//
//
//        $blackFriday  = BlackFridayRequest::where([
//            "store_id"=>$this->CURRENT_USER->id,
//        ])/*->isActive($this->CURRENT_USER->id)*/->orderBy("id","desc")->first();
////dd($blackFriday);
//
//
//        $reelsRandomVideos = Post::where(function ($q){
//            $q->whereHas("store")->orWhereHas("customer");
//        })->whereNotNull("media_thumbnail")
//            ->isReels()
//            ->inRandomOrder(/*$request->session()->get("randomVideoSeed")*/)
//            ->limit(5)->get();
//
//
//        $result = [
//            "is_hot_sale_active" =>$blackFriday->isActive() ,
//            'posts' => PostResource::collection($data),
//            'reels' => PostResource::collection($reelsRandomVideos),
//            'stores'=>StoreResource::collection($this->getRecommended_stores()),
//
//            "pagination" => [
//                "i_total_objects" => $data->count(),
//                "i_items_on_page" => $data->count(),
//                "i_per_pages" => $data->perPage(),
//                "i_current_page" => $data->currentPage(),
//                "i_total_pages" => $data->total()
//            ]
//
//        ];
//
//        return $this->sendResponse($result, '');
//    }


//    public function index(Request $request)
//    {
//        $currentUserStoreType = $this->CURRENT_USER->type; // Adjust this if necessary to get the user's store type
//
//        $data = Post::isNotReels()->where('store_id', $this->CURRENT_USER->id);
//
//        if ($request->type == Post::$HOT_SALE_TYPE) {
//            $data->IsHotSale();
//        } else {
//            $data->IsNotHotSale();
//        }
//
//        if ($request->search_name) {
//            $data->where('description', 'LIKE', '%' . $request->search_name . '%');
//        }
//
//        $data = $data->orderBy('created_at', 'desc')->paginate(12);
//
//        $blackFriday  = BlackFridayRequest::where([
//            'store_id' => $this->CURRENT_USER->id,
//        ])->orderBy('id', 'desc')->first();
//
//        // Fetch reels random videos
//        $reelsRandomVideos = Post::where(function ($q) {
//            $q->whereHas('store')->orWhereHas('customer');
//        })
//            ->whereNotNull('media_thumbnail')
//            ->isReels()
//            ->inRandomOrder()
//            ->limit(5)
//            ->get();
//
//        // Fetch posts where store type is 'wholesale'
//
//        if ($currentUserStoreType === 'wholesaler') {
//            // If current user store type is wholesale, get posts from stores that are not retailer and created in the last month
//            $wholesalePosts = Post::where(function ($q) {
//                $q->whereHas('store', function ($query) {
//                    $query->where('type', '!=', 'wholesaler'); // Ensure store type is not 'retailer'
//                });
//            })
//                ->where('created_at', '>=', Carbon::now()->subMonth(5)) // Filter for posts created in the last month
//                ->inRandomOrder()
//                ->limit(50)
//                ->get();
//        } else {
//            // If current user store type is retailer, get posts from stores that are not wholesale and created in the last month
//            $wholesalePosts = Post::where(function ($q) {
//                $q->whereHas('store', function ($query) {
//                    $query->where('type', '!=', 'retailer'); // Ensure store type is not 'wholesale'
//                });
//            })
//                ->where('created_at', '>=', Carbon::now()->subMonth(5)) // Filter for posts created in the last month
//                ->inRandomOrder()
//                ->limit(50)
//                ->get();
//        }
//
//
//
//        $result = [
//            'is_hot_sale_active' => $blackFriday->isActive(),
//            'posts' => PostResource::collection($wholesalePosts),
//            'reels' => PostResource::collection($reelsRandomVideos),
//            'wholesale_posts' => PostResource::collection($wholesalePosts), // Add wholesale posts here
//            'stores' => StoreResource::collection($this->getRecommended_stores()),
//
//            'pagination' => [
//                'i_total_objects' => $data->count(),
//                'i_items_on_page' => $data->count(),
//                'i_per_pages' => $data->perPage(),
//                'i_current_page' => $data->currentPage(),
//                'i_total_pages' => $data->total(),
//            ]
//        ];
//
//        return $this->sendResponse($result, '');
//    }


//    public function index(Request $request)
//    {
//        $currentUserStoreType = $this->CURRENT_USER->type; // Adjust this if necessary to get the user's store type
//
//        // Start building the base query
//        $data = Post::isNotReels()->where('store_id', $this->CURRENT_USER->id);
//
//        // Check if hot sale filter is applied
//        if ($request->type == Post::$HOT_SALE_TYPE) {
//            $data->IsHotSale();
//        } else {
//            $data->IsNotHotSale();
//        }
//
//        // Apply logic based on the user store type
//        $wholesalePostsQuery = Post::where(function ($q) use ($currentUserStoreType) {
//            $q->whereHas('store', function ($query) use ($currentUserStoreType) {
//                if ($currentUserStoreType === 'wholesaler') {
//                    $query->where('type', '!=', 'wholesaler'); // If wholesaler, exclude 'retailer'
//                } else {
//                    $query->where('type', '!=', 'retailer');   // If retailer, exclude 'wholesaler'
//                }
//            });
//        });
//             // Filter for posts created in the last 5 months
//
//        // Apply search filter before getting the posts
//        if ($request->search_name) {
//            $wholesalePostsQuery->whereRaw("description COLLATE utf8mb4_unicode_ci LIKE ?", ['%' . $request->search_name . '%'])
//                ->where('created_at', '>=', Carbon::now()->subMonth(50));
//        }
//        else{
//            $wholesalePostsQuery->where('created_at', '>=', Carbon::now()->subMonth(5));
//        }
//
//
//
//        // Retrieve the wholesale posts
//        $wholesalePosts = $wholesalePostsQuery->inRandomOrder()->limit(50)->get();
//
//        // Paginate data
//        $data = $data->orderBy('created_at', 'desc')->paginate(12);
//
//        // Fetch BlackFriday request
//        $blackFriday = BlackFridayRequest::where([
//            'store_id' => $this->CURRENT_USER->id,
//        ])->orderBy('id', 'desc')->first();
//
//        // Fetch reels random videos
//        $reelsRandomVideos = Post::where(function ($q) {
//            $q->whereHas('store')->orWhereHas('customer');
//        })
//            ->whereNotNull('media_thumbnail')
//            ->isReels()
//            ->inRandomOrder()
//            ->limit(5)
//            ->get();
//
//        // Construct the result array
//        $result = [
//            'is_hot_sale_active' => $blackFriday->isActive(),
//            'posts' => PostResource::collection($wholesalePosts),
//            'reels' => PostResource::collection($reelsRandomVideos),
//            'wholesale_posts' => PostResource::collection($wholesalePosts),
//            'stores' => StoreResource::collection($this->getRecommended_stores()),
//            'pagination' => [
//                'i_total_objects' => $data->count(),
//                'i_items_on_page' => $data->count(),
//                'i_per_pages' => $data->perPage(),
//                'i_current_page' => $data->currentPage(),
//                'i_total_pages' => $data->total(),
//            ]
//        ];
//
//        return $this->sendResponse($result, '');
//    }

    protected $storageService;

    public function __construct(S3StorageService $storageService)
    {
        parent::__construct();
        $this->storageService = $storageService;
    }
    public function index(Request $request)
    {
        $currentUserStoreType = $this->CURRENT_USER->type;

        // Start building the base query for the current user's posts
        $data = Post::isNotReels()->where('store_id', $this->CURRENT_USER->id);

        // Check if hot sale filter is applied (Black Friday sale)
        if ($request->type == Post::$HOT_SALE_TYPE) {
            // Filter posts specifically for Black Friday Hot Sale for the current user only
            $data->IsHotSale();
        } else {
            $data->IsNotHotSale();
        }

        // Apply logic based on the user store type, exclude specific types based on user role
        $wholesalePostsQuery = Post::where(function ($q) use ($currentUserStoreType) {
            $q->whereHas('store', function ($query) use ($currentUserStoreType) {
                if ($currentUserStoreType === 'wholesaler') {
                    $query->where('type', '!=', 'wholesaler'); // If wholesaler, exclude wholesaler type
                } else {
                    $query->where('type', '!=', 'retailer');   // If retailer, exclude retailer type
                }
            });
        });

        // Apply search filter and time range for post creation dates
        if ($request->search_name) {
            $wholesalePostsQuery->whereRaw("description COLLATE utf8mb4_unicode_ci LIKE ?", ['%' . $request->search_name . '%'])
                ->where('created_at', '>=', Carbon::now()->subMonth(50)); // If search query exists, filter last 50 months
        } else {
            $wholesalePostsQuery->where('created_at', '>=', Carbon::now()->subMonth(5)); // Otherwise, filter posts from last 5 months
        }

        // Retrieve wholesale posts with random order and limit to 50
        $wholesalePosts = $wholesalePostsQuery->inRandomOrder()->limit(50)->get();

        // Paginate the main query for posts
        $data = $data->orderBy('created_at', 'desc')->paginate(12);

        // Fetch the latest BlackFriday request for the current user
        $blackFriday = BlackFridayRequest::where([
            'store_id' => $this->CURRENT_USER->id,
        ])->orderBy('id', 'desc')->first();

        // Fetch random reels videos
        $reelsRandomVideos = Post::where(function ($q) {
            $q->whereHas('store')->orWhereHas('customer');
        })
            ->whereNotNull('media_thumbnail')
            ->isReels()
            ->inRandomOrder()
            ->limit(5)
            ->get();

        if ($request->type == Post::$HOT_SALE_TYPE) {
            $result = [
                'is_hot_sale_active' => $blackFriday && $blackFriday->isActive() ? true : false,
                'posts' => PostResource::collection($data), // Changed to current user's posts
                'reels' => PostResource::collection($reelsRandomVideos),
                'wholesale_posts' => PostResource::collection($wholesalePosts),
                'stores' => StoreResource::collection($this->getRecommended_stores()),
                'pagination' => [
                    'i_total_objects' => $data->total(),
                    'i_items_on_page' => $data->count(),
                    'i_per_pages' => $data->perPage(),
                    'i_current_page' => $data->currentPage(),
                    'i_total_pages' => $data->lastPage(),
                ]
            ];
        }else{
            $result = [
                'is_hot_sale_active' => $blackFriday && $blackFriday->isActive() ? true : false,
                'reels' => PostResource::collection($reelsRandomVideos),
                'posts' => PostResource::collection($wholesalePosts),
                'stores' => StoreResource::collection($this->getRecommended_stores()),
                'pagination' => [
                    'i_total_objects' => $data->total(),
                    'i_items_on_page' => $data->count(),
                    'i_per_pages' => $data->perPage(),
                    'i_current_page' => $data->currentPage(),
                    'i_total_pages' => $data->lastPage(),
                ]
            ];
        }
        return $this->sendResponse($result, '');
    }


    public function store(Request $request)
    {
        $this->validate(
            $request,
            [
                'description' => 'required',
                //   'media' => 'mimes:jpeg,bmp,png,gif,svg,pdf,txt,doc,docx,psd,zip,rar,xlsx,pptx,ppt,mp4,mov,mpeg,mpg,wmv,avi,mp3',
            ],[],[
                "description" => \Lang::get("lang.post_description")
            ]
        );

        $filename = null;

        $create = Post::create([

            "description" => $request->description,
            // "status" => $request->status == "published" ? "published" : "draft",
             "status" =>  "published",
            "store_id" => $this->CURRENT_USER->id ,
            "type" => $request->type==Post::$HOT_SALE_TYPE?Post::$HOT_SALE_TYPE:null,
            "start_date"=>$request->start_date,
            "end_date"=>$request->end_date,

        ]);

        if ($request->file("video")) {

            $filename = $request->file("video")->store('/videos', 'public');
          //  $create->media = $filename;
            $create->media = VideoEdit::compressVideo($filename);
            $videoFileContents = Storage::disk('public')->get($create->media);
            $this->storageService->uploadFile($create->media, $videoFileContents);

            $ext = pathinfo($filename, PATHINFO_EXTENSION);
            $videoExtension = ['mp4', 'mov', 'mpg', 'mpeg', 'wmv', 'mkv', 'avi', 'ts', 'TS'];

            if (in_array($ext, $videoExtension)) {
                $create->media_thumbnail = VideoEdit::generateVideoThumbnail($create->media);
                $videoFileContents = Storage::disk('public')->get($create->media_thumbnail);
                $this->storageService->uploadFile($create->media_thumbnail, $videoFileContents);

            }

            $create->save();
        }

        if ($request->media)
            foreach ($request->media as $row) {
                $filename = $row->store('/', 'public');
                PostMedia::create([
                    'post_id' => $create->id,
                    'image' => $filename,
                ]);
            }


             SendNotification::sendPostPublishedNotification($this->CURRENT_USER , $create);

        return $this->sendResponse(new PostResource(Post::find($create->id)), '');
    }



    public function update(Request $request)
    {
        $this->validate(
            $request,
            [
                "post_id" => "required|exists:posts,id",
                'description' => 'required',
            ]
        );
        $post = Post::where("id", $request->post_id)->where("store_id", $this->CURRENT_USER->id)->first();
        if (is_null($post)) {
            return $this->sendError( \Lang::get("lang.no_permissions"), 422);
        }
        $filename = $request->video ? $request->file("video")
            ->store('/', 'public') : $post->video;




        if ($request->file("video")) {

            $filename = $request->file("video")->store('/videos', 'public');

            $filename = VideoEdit::compressVideo($filename);
            $videoFileContents = Storage::disk('public')->get($filename);
            $this->storageService->uploadFile($filename, $videoFileContents);

            $ext = pathinfo($filename, PATHINFO_EXTENSION);
            $videoExtension = ['mp4', 'mov', 'mpg', 'mpeg', 'wmv', 'mkv', 'avi', 'ts', 'TS'];

            if (in_array($ext, $videoExtension)) {
                $post->media_thumbnail = VideoEdit::generateVideoThumbnail($filename);
                $videoFileContents = Storage::disk('public')->get($post->media_thumbnail);

                $this->storageService->uploadFile($post->media_thumbnail, $videoFileContents);

            }

        }




        $post->update([
            "description" => $request->description,
            "media" => $filename,
            // "status" => $request->status == "published" ? "published" : "draft",
             "status" =>"published",
            "store_id" => $this->CURRENT_USER->id,
            "start_date"=>$request->start_date,
            "end_date"=>$request->end_date,
        ]);

        //PostMedia::where('post_id',$post->id)->delete();

        foreach ($request->media as $row) {
            $filename = $row->store('/', 'public');
            PostMedia::create([
                'post_id' => $post->id,
                'image' => $filename,
            ]);
        }

        return $this->sendResponse($post, '');

        // return $this->sendResponse(new PostResource(Post::find($post->id)), '');
    }

    public function delete(Request $request)
    {
        $this->validate(
            $request,
            [
                'id' => "required|exists:posts,id",
            ]
        );

        $post = Post::where("id", $request->id)->where("store_id", $this->CURRENT_USER->id)->first();
        if (is_null($post)) {
            return $this->sendError( \Lang::get("lang.no_permissions"), 422);
        }
        $post->delete();
        return $this->sendResponse(null,  \Lang::get("lang.deleted_successfully"));
    }

    public function imageDelete(Request $request)
    {
        $this->validate(
            $request,
            [
                'post_id' => "required|exists:posts,id",
                'image_id' => "required",

            ]
        );

        $product = PostMedia::where("id", $request->image_id)
            ->where("post_id", $request->post_id)
            ->first();

        if (is_null($product)) {
            return $this->sendError(\Lang::get("lang.deleted_successfully"), 422);
        }
        $product->delete();
        return $this->sendResponse(null, \Lang::get("lang.deleted_successfully"));
    }


        public function getPost($post_id)
        {

            $post = null;

            if ($post_id) {
                $post = Post::find($post_id);
                if (is_null($post)) {
                    return $this->sendError(\Lang::get("lang.not_found"), 422);
                }
            }

            return $this->sendResponse(new PostResource(Post::find($post->id)), '');


        }

        public function getPostComments(Request $request ,$post_id ){
//        $this->validate($request,[
//            'post_id'=>'required',
//        ]);
                $post =null;



                if ($post_id){
                    $post = Post::find($post_id);
                    if(is_null($post)){
                        return $this->sendError(' غير موجود',422);
                    }
                }


                $comments = Comment::where("post_id",$post->id)
                    ->whereNull("parent_id")
                    ->orderByRaw("id desc ")
                    ->paginate(12);



                $result = [
                    'comment' => \App\Http\Resources\CommentResource::collection($comments),
                    "pagination" => [
                        "i_total_objects" => $comments->count(),
                        "i_items_on_page" => $comments->count(),
                        "i_per_pages" => $comments->perPage(),
                        "i_current_page" => $comments->currentPage(),
                        "i_total_pages" => $comments->total()
                    ]
                ];
                return $this->sendResponse($result,'success');
        }

    public function replyComment(Request $request ,$post_id , $comment_id){
        $this->validate($request,[

            'comment_text'=>'required',
        ]);

        $comment = Comment::find($comment_id);


        $customer = Customer::find($comment->customer_id);
        //$post = Post::find($request->post_id);

        if(is_null($comment)){
            return $this->sendError('Comment not found',404);
        }


        SendNotification::sendReplyOnComment($this->CURRENT_USER,$customer,$post_id);

        $create = Comment::create([
            'customer_id'=>$comment->customer_id,
            "post_id"=>$comment->post_id,
            "parent_id"=>$comment->id,
            "comment_text"=>$request->comment_text,
        ]);

        return $this->sendResponse(new \App\Http\Resources\CommentResource($create),\Lang::get("lang.added_successfully"));
    }
    public function deleteComment(Request $request ,$post_id , $comment_id){

        $comment = Comment::find($comment_id);
       // dd($comment,$comment->post,$this->CURRENT_USER->id);

        if(is_null($comment) ){
            return $this->sendError( \Lang::get("lang.not_found"),404);
        }elseif ($comment->post->store_id != $this->CURRENT_USER->id ){
            return $this->sendError( \Lang::get("lang.no_permissions"),401 );
        }

        Comment::where("parent_id",$comment->id)->delete();
        $comment->delete();

        return $this->sendResponse(null,  \Lang::get("lang.deleted_successfully"));

    }

    public function getRecommended_stores(){

        session(['storesSeed' => random_int(PHP_INT_MIN,PHP_INT_MAX)]);

        $data = Store::isActive()->IsWholeSale();

        return  $data->inRandomOrder(session("storesSeed"))->paginate(5);

    }


}
