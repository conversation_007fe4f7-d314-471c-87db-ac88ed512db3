<?php

namespace App\Http\Controllers\Store;

use App\Http\Resources\StoreResource;
use App\Models\Store;
use App\Models\User;
use App\Services\CometChat;
use App\Services\SocialLogin;
use Illuminate\Http\Request;
use App\Http\Controllers\BaseControllerApi;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Lang;
use Validator;

use App\Models\Customer;

class StoreAuthApiController extends BaseControllerApi
{

    public function login(Request $request)
{        
    
    if (!isset($request->provider) || $request->provider === 'emailPassword') {
        $this->validate($request, [
            'email' => 'required|email',
            'password' => 'required',
        ]);
        // Retrieve the store using the email
        $user = Store::where("email", $request->email)->first();
        if (!$user) {
            return $this->sendError(\Lang::get("lang.user_not_found"), 404);
        }

        // Check if the store exists and if the password is correct
        if (!$user || !\Hash::check($request->password, $user->password)) {
            return $this->sendError(\Lang::get("lang.wrong_credentials"), 422);
        }

        // Check if the store requires SMS verification
        if ($user->sms_verify == false) {
            $token = \Str::random(64);
            $sms_code = generateNDigitRandomNumber(6);

            $user->sms_code = $sms_code;
            $user->token = $token;
            $user->save();

            // Send the verification code via email
            \Mail::to($user->email)->send(new \App\Mail\VerifyEmail($user->sms_code));

            $data = [
                'sms_verify' => false,
                'sms_code' => $user->sms_code,
                "token" => $token
            ];

            return $this->sendResponse($data, \Lang::get("lang.otp_sent"));
        }

    } else {
        $this->validate($request, [
            'access_token' => 'nullable',
            'authCode' => 'nullable',
            'provider' => 'required'
        ]);
        $user = SocialLogin::storeSocialLogin($request->access_token, $request->authCode, $request->provider);
    }
    if (!$user) {
        return $this->sendError(\Lang::get("lang.user_not_found"), 404);
    }
    // Check if the account is blocked
    if ($user->status == false) {
        return $this->sendResponse(null, \Lang::get("lang.account_blocked"));
    }

    // Generate an authentication token
    $token = $user->createToken($request->header('X-Client-Device-Name'))->plainTextToken;

    // Register the user in CometChat if not already registered
    if (!$user->is_chat_register) {
        $chat = new CometChat();
        $chat->create_user($user, false, "store_" . $user->id);
    }

    // Prepare the response
    $result = [
        'user' => new StoreResource($user),
        'token' => $token,
    ];

    return $this->sendResponse($result, '');
}


    public function sendForgetPassword(Request $request)
{
    // Validate that the email field is required and properly formatted
    $this->validate($request, ['email' => 'required|email']);

    // Find the store by email
    $user = \App\Models\Store::where('email', $request->email)->first();

    if (is_null($user)) {
        return $this->sendError(\Lang::get("lang.not_registered"), 422);
    }

    // Reset the sms_verify status
    $user->sms_verify = false;

    // Generate the verification code
    $sms_code = generateNDigitRandomNumber(6);

    // Save the code and token to the user record
    $user->sms_code = $sms_code;
    $user->token = \Str::random(32);
    $user->save();

    // Prepare the result data to send back to the frontend
    $result = [
        'sms_verify' => false,
        'sms_code' => $user->sms_code, // For debugging, consider removing in production
        "token" => $user->token,
    ];

    // Send the verification code via email
    \Mail::to($user->email)->send(new \App\Mail\VerifyEmail($user->sms_code));

    return $this->sendResponse($result, \Lang::get("lang.otp_sent"));
}

    public function set_forget_password(Request $request){
        $this->validate($request, ['new_password' => 'numeric|required']);
        $passwordLast = bcrypt($request->new_password);
        $this->CURRENT_USER->password=$passwordLast;
        $this->CURRENT_USER->save();

        return $this->sendResponse(null,\Lang::get("lang.password_reset"));


    }

    public function logout(Request $request){
        $this->CURRENT_USER->save();
        $request->user()->currentAccessToken()->delete();
     //   auth()->logout();
        return $this->sendResponse(null,\Lang::get("lang.logged_out"));

    }

    public function getProfile(Request $request){

        return $this->sendResponse(["user"=>new StoreResource($this->CURRENT_USER)],null);

    }

    public  function generateNDigitRandomNumber($length){
        return mt_rand(pow(10,($length-1)),pow(10,$length)-1);
    }


public function register(Request $request)
{
    // Validation rules, similar to customer registration
    $this->validate($request, [
        'name' => 'required',
        'email' => 'required|email|unique:stores,email',
        'logo' => 'nullable|mimes:jpeg,bmp,png,gif,svg,pdf,txt,doc,docx,psd,zip,rar,xlsx,pptx,ppt,mp4,mov,mpeg,mpg,wmv,avi,mp3',
        'governorate_id' => 'nullable|exists:governorates,id',
        'country_id' => 'required|exists:countries,id',
        'type' => 'required',
        'mobile' => 'required',
        'city_id' => 'nullable|exists:cities,id',
        'address' => 'required',
        'store_category_id' => 'required|exists:store_categories,id',
        'password' => 'nullable'
    ], [], [
        "store_category_id" => Lang::get("lang.store_category"),
        "type" => Lang::get("lang.sales_type"),
        "address" => Lang::get("lang.address"),
        "country_id" => Lang::get("lang.country"),
    ]);

    $email_verification_code = \Str::random(6); // Generate a 6-digit verification code

    // Handle file upload for logo
    $logo = null;
    if ($request->hasFile('logo')) {
        $logo = $request->file('logo')->store('/', 'public');
    }

    // Create the store without token
    $store = Store::create([
        'name' => $request->name,
        'mobile'  => $request->mobile,
        'full_mobile'  => $request->mobile,
        'email' => $request->email,
        'country_id' => $request->country_id,
        'governorate_id' => $request->governorate_id,
        'city_id' => $request->city_id,
        'region_id' => $request->region_id,
        'address' => $request->address,
        'category_id' => $request->store_category_id,
        'type' => $request->type,
        'password' => Hash::make($request->password),
        'sms_code' => $email_verification_code, // Verification code
        'sms_verify' => false,
        'is_chat_register' => false,
        'logo' => $logo, // Store logo if provided
    ]);

    // Generate a token and update the store
    $token = $store->createToken($request->header('X-Client-Device-Name'))->plainTextToken;
    $store->token = $token;

    $emailVerified = false;

    // TODO: convert to db logic
    $user = Customer::where("email", $store->email)->first();
    if (isset($user->provider) && isset($user->provider_id) && $user->provider && $user->provider_id) {
        $store->sms_verify = true;
        $store->status = true;
        $emailVerified = true;
    }

    $store->save();

    // Send verification email
    \Mail::to($store->email)->send(new \App\Mail\VerifyEmail($store->sms_code));

    // Prepare the response data
    $result = [
        'email_verified' => $emailVerified,
        'token' => $token,
    ];

    // Register store in CometChat if not already registered
    if (!$store->is_chat_register) {
        $chat = new CometChat();
        $chat->create_user($store, false, "store_" . $store->id);

    }
    

    // Return a success response
    return $this->sendResponse($result, \Lang::get("lang.registered_successfully"));
}



    public function verifySmsCode(Request $request){
        $this->validate($request,[
            "code"=>"required",
            "token"=>"required"
        ]);

        $getCustomer = Store::where('sms_code',$request->code)
            ->where('sms_verify',false)
            // ->where('token',$request->token) بكرا بنشوف 
            ->first();
            
        if(is_null($getCustomer)){
            return $this->sendError(\Lang::get("lang.wrong_otp"),422);
        }

        $getCustomer->sms_verify = true;
        $getCustomer->status = true;
        $getCustomer->save();

        addBlackFridaySubscription($getCustomer , 0 );

        $token =$getCustomer->createToken($request->header('X-Client-Device-Name'))->plainTextToken;
        $data = [
            "user"=> new StoreResource($getCustomer),
            "token"=>$token,
        ];
        return $this->sendResponse($data,\Lang::get("lang.valid_otp"));


    }

    public function smsCode(Request $request){
        // dd($this->CURRENT_USER);
        $this->validate($request,[
            'sms_code'=>'required'
        ]);


        if ($this->CURRENT_USER->sms_code == $request->sms_code) {

            $this->CURRENT_USER->sms_verify=true;
            $this->CURRENT_USER->save();
            $token =$this->CURRENT_USER->createToken($request->header('X-Client-Device-Name'))->plainTextToken;
            $result = [
                'user'=>$this->CURRENT_USER,
                'token'=>$token,
            ];
            return $this->sendResponse($result,'');

        }


        return $this->sendError(\Lang::get("lang.wrong_otp"),422);

    }



    public function fcmSend(Request $request){

        $this->validate($request, [
            'device_type' =>'required',
            'fcm_token'=>'required',
        ]);
        $this->CURRENT_USER->device_type =$request->device_type;
        $this->CURRENT_USER->fcm_token =$request->fcm_token;
        $this->CURRENT_USER->save();
        return $this->sendResponse($this->CURRENT_USER,'');
    }

    public function changPassword(Request $request){
        $this->validate($request, ['old_password' => 'required','new_password' => 'required']);


        $old_password = $request->old_password;
        if(Hash::check($old_password, $this->CURRENT_USER->password)){
            $passwordLast = bcrypt($request->new_password);
            $this->CURRENT_USER->password=$passwordLast;
            $this->CURRENT_USER->save();
            return $this->sendResponse(null,\Lang::get("lang.password_changed"));
        }

        return $this->sendError(\Lang::get("lang.invalid_old_password"),422);

    }

    public function deleteAccount (Request $request){

        $customer = Store::find($this->CURRENT_USER->id);
        $customer->phone = $customer->phone."_delete".$this->CURRENT_USER->id;
        $customer->email = $customer->email."_delete".$this->CURRENT_USER->id;
        $customer->save();
        $customer->delete();
        return $this->sendResponse(null,\Lang::get("lang.account_deleted"));
    }


    public function saveProfile(Request $request){
        $customer = $this->CURRENT_USER;

        $full_mobile = getFullMobile($request->phone_code ,$request->mobile );
        $request->merge(["full_mobile" => $full_mobile ]);

        $this->validate($request, [
            'name'=>'required',
            'mobile'=>'required',
            'phone_code'=>'required',
            'full_mobile'=>'required|unique:stores,full_mobile,'.$customer->id,

        //    'email'=>'required|email|unique:stores,email,'.$customer->id,
            'logo'=>'nullable|mimes:jpeg,bmp,png,gif,svg,pdf,txt,doc,docx,psd,zip,rar,xlsx,pptx,ppt,mp4,mov,mpeg,mpg,wmv,avi,mp3',
            'country_id'=>'required|exists:countries,id',
            'governorate_id'=>'required|exists:governorates,id',
            'city_id'=>'required|exists:cities,id',
            'type'=>'required',
         //   'region_id'=>'required|exists:regions,id',
            //'address'=>'required',
            'store_category_id'=>'required|exists:store_categories,id',
           // 'password'=>'required',
            'opening_type'=>'required',
        ],[],[
            "store_category_id"=>Lang::get("lang.store_category"),
            "type"=>Lang::get("lang.sales_type"),
            "address"=>Lang::get("lang.address"),
            "country_id"=> Lang::get("lang.country"),
            "full_mobile"=> Lang::get("lang.mobile")

        ]);

        \DB::beginTransaction();
        try {

            if ($request->phone_code =="90"){//TR
                $sms_code = (int)"111111" ;
            }else
                $sms_code=generateNDigitRandomNumber(6);

            $filename = $request->file("logo")?$request->file("logo")->store('/','public'):$customer->logo;
            $customer->update([
                'name'=>$request->name,
                'mobile'=>$request->mobile,
                'code'=>$request->code,
                'phone_code'=>$request->phone_code,
                'email'=>$request->email,
                'country_id'=>$request->country_id,
                'governorate_id'=>$request->governorate_id,
                'city_id'=>$request->city_id,
                'region_id'=>$request->region_id,
                'category_id'=>$request->store_category_id,
                'currency_id'=>$request->currency_id,
                'bio'=>$request->bio,
                'whatsapp'=>$request->whatsapp,
                'whatsapp_phone_code'=>$request->whatsapp_phone_code,
                'facebook'=>$request->facebook,
                'instagram'=>$request->instagram,
                'phone'=>$request->phone,
                'open_type'=>$request->opening_type,
                'open_time_from'=>$request->opening_form_time,
                'open_time_to'=>$request->opening_to_time,
                "open_days" =>$request->open_days,
                "address" =>$request->address,
                "type" =>$request->type,

                'logo'=>$filename ,
            ]);
            \DB::commit();

            $chat = new CometChat();
            $chat->create_user($customer,true,"store_".$customer->id);

            return $this->sendResponse( new StoreResource($customer),'');

        } catch (\Exception $e) {

            \DB::rollback();
            return $this->sendError($e->getMessage(),422);
        }
    }

    public function smsResend(Request $request)
{
    // Validate that the email field is required and properly formatted
    $this->validate($request, [
        "email" => "required|email",
    ]);

    // Find the store by email
    $user = \App\Models\Store::where("email", $request->email)->first();

    if (is_null($user)) {
        return $this->sendError(\Lang::get("lang.not_registered"), 422);
    }

    // Generate the verification code
    $sms_code = generateNDigitRandomNumber(6);

    // Generate a new token
    $token = \Str::random(32);

    // Update the user with the new verification code and token
    $user->sms_code = $sms_code;
    $user->token = $token;
    $user->save();

    // Send the verification code via email
    \Mail::to($user->email)->send(new \App\Mail\VerifyEmail($sms_code));

    return $this->sendResponse(null, \Lang::get("lang.code_resent"));
}


    public function createUsers($store,$password){
        $create = User::create([
            'email'=>time()."_".$store->mobile."@wintoo.store",
            'name'=>$store->name,
            'password'=> bcrypt($password),
            'role_id' =>1,
            'type' =>"STORE",
            'store_id' =>$store->id,
            "status"=>true,
        ]);

    }
}
