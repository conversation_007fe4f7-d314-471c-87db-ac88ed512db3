<?php

namespace App\Http\Controllers\Store;

use App\Http\Controllers\Controller;
use App\Http\Resources\AddressResource;
use App\Http\Resources\AreaResource;
use App\Http\Resources\NotificationApiResource;
use App\Http\Resources\SystemDrawDashboardResource;
use App\Http\Resources\SystemDrawResource;
use App\Models\Address;
use App\Models\City;
use App\Models\Faq;
use App\Models\Governorate;
use App\Models\Post;
use App\Models\Region;
use App\Models\StoreCategory;
use App\Models\SystemDraw;
use Illuminate\Http\Request;
use App\Http\Controllers\BaseControllerApi;
use Illuminate\Support\Facades\Hash;


use App\Models\Customer;

class StoreDrawApiController extends BaseControllerApi
{
    public function index(Request $request){
        $data = SystemDraw::where('store_id',$this->CURRENT_USER->id);
        if($request->search_name){
            $data->where('title',"LIKE","%".$request->search_name."%");
        }

        //    $data =  $data->orderBy("created_at" , "desc");



        $data =  $data->orderBy("created_at" , "desc")->paginate(12);

        $result= [
            'draws'=>SystemDrawDashboardResource::collection($data),
            "pagination"=> [
                "i_total_objects"=>$data->count(),
                "i_items_on_page"=> $data->count(),
                "i_per_pages"=>$data->perPage(),
                "i_current_page"=>$data->currentPage() ,
                "i_total_pages"=> $data->total()
            ]

        ];
        return $this->sendResponse($result,'');
    }

    public function store(Request $request){
        $this->validate($request, [
            'title' => 'required',
            "prize_name" =>'required',
            //  "status" =>'required',
        ]);

        $date = $request->date ;

        $create = SystemDraw::create([
            "title"=>$request->title,
            "prize_name"=>$request->prize_name,
            "status"=>$request->status=="published"?"published":"published",
            "store_id"=>$this->CURRENT_USER->id,
            "date"=>toUTCDate($request->date),
        ]);
//        if($request->status=="published"){
//
//        }

        $res = $create->toArray();
        $res["date"] = toLocalDate($create->date);
        $res["datetime"]  =   toLocalDate($create->draw_date_format , 'Y/m/d H:i:s');
        return $this->sendResponse(($res),'');

    }

    public function update(Request $request){

        $this->validate($request, [
            "system_draw_id" =>"required|exists:system_draws,id",
            'title' => 'required',
            "prize_name" =>'required',
            //  "status" =>'required',
        ]);
        $draw = SystemDraw::where("id",$request->system_draw_id)
            ->where("store_id",$this->CURRENT_USER->id)->first();
        if (is_null($draw)){
            return $this->sendError(\Lang::get("lang.no_permissions"),422);
        }


        $draw->update([
            "title"=>$request->title,
            "prize_name"=>$request->prize_name,
            "status"=>$request->status=="published"?"published":"published",
            "store_id"=>$this->CURRENT_USER->id,
            "date"=>toUTCDate($request->date),
        ]);


        $res = $draw->toArray();
        $res["date"] = toLocalDate($draw->date);
        $res["datetime"]  =   toLocalDate($draw->draw_date_format , 'Y/m/d H:i:s');
        return $this->sendResponse(($res),'');


    }

    public function delete(Request $request){
        $this->validate(
            $request,
            [
                'id' => "required|exists:system_draws,id",
            ]
        );

        $post = SystemDraw::where("id",$request->id)
            ->where("store_id",$this->CURRENT_USER->id)->first();
        if (is_null($post)){
            return $this->sendError(\Lang::get("lang.no_permissions"),422);
        }
        $post->delete();
        return $this->sendResponse(null,\Lang::get("lang.deleted_successfully"));
    }

}
