<?php

namespace App\Http\Controllers\Store;

use App\Http\Controllers\Controller;
use App\Http\Resources\AreaResource;
use App\Http\Resources\AttributeResource;
use App\Http\Resources\BlackFridayRequestResource;
use App\Http\Resources\CustomerResource;
use App\Http\Resources\NotificationApiResource;
use App\Http\Resources\PostResource;
use App\Http\Resources\ProductHomeResource;
use App\Http\Resources\StoreCategoryResource;
use App\Http\Resources\StoreOfferResource;
use App\Http\Resources\StoreResource;
use App\Http\Resources\SystemDrawResource;
use App\Http\Resources\SystemQrRequestResource;
use App\Models\Attribute;
use App\Models\BlackFridayRequest;
use App\Models\City;
use App\Models\Faq;
use App\Models\Follower;
use App\Models\Governorate;
use App\Models\Offer;
use App\Models\Payment;
use App\Models\Post;
use App\Models\Product;
use App\Models\ProductsRequestsCategory;
use App\Models\QRRequest;
use App\Models\Region;
use App\Models\Setting;
use App\Models\Store;
use App\Models\StoreCategory;
use App\Models\SystemDraw;
use App\Services\CandidateTable;
use App\Services\OrderTransaction;
use App\Services\SendNotification;
use Illuminate\Http\Request;
use App\Http\Controllers\BaseControllerApi;
use Illuminate\Support\Facades\Hash;
use Kreait\Firebase\Factory;
use Kreait\Firebase\Messaging\CloudMessage;
use Kreait\Firebase\Messaging\Notification;


use App\Models\Customer;

class StoreGeneralApiController extends BaseControllerApi
{

    public function getStoreCategory(Request $request){

        if($request->id){
            //$category =  StoreCategory::find($request->id);

            $category =  ProductsRequestsCategory::where("id",$request->id);
            if ($request->type){
                $category->where("type",$request->type);
            }
            $category = $category->get()->first();
            $attributes = $category->attributes;

            return $this->sendResponse(["attributes"=>AttributeResource::collection($attributes)],'');
        }else{
            $result = [
                'categories'=>StoreCategoryResource::collection(StoreCategory::all())
            ];
        }


        return $this->sendResponse($result,'');

    }
    public function getProductsRequestCategories(Request $request){
        if($request->id){
            $category =  ProductsRequestsCategory::find($request->id);
            $attributes = $category->attributes;

            return $this->sendResponse(["attributes"=>AttributeResource::collection($attributes)],'');
        }else{
            $result = [
                'categories'=>StoreCategoryResource::collection(ProductsRequestsCategory::where("type","product_request")->get())
            ];
        }


        return $this->sendResponse($result,'');

    }


    public function getRentalRequestCategories(Request $request){
        if($request->id){
            $category =  ProductsRequestsCategory::find($request->id);
            $attributes = $category->attributes;

            return $this->sendResponse(["attributes"=>AttributeResource::collection($attributes)],'');
        }else{
            $result = [
                'categories'=>StoreCategoryResource::collection(ProductsRequestsCategory::where("type","rental_request")->get())
            ];
        }


        return $this->sendResponse($result,'');

    }

    public function follows(Request $request){

        $customers = optional($this->CURRENT_USER)->follower();

        if ($request->search_name){
            $customers->where('username',"LIKE",'%'.$request->search_name."%");
        }

        $customers =  $customers->paginate(10);


        $result = [
            'customers'=>CustomerResource::collection($customers),
            "pagination"=>[
                "i_total_objects"=>$customers->count(),
                "i_items_on_page"=> $customers->count(),
                "i_per_pages"=>$customers->perPage(),
                "i_current_page"=>$customers->currentPage() ,
                "i_total_pages"=> $customers->total()
            ]
        ];

        return $this->sendResponse($result,'');

    }


    public function Notifyfollows(Request $request){

        $customers = optional($this->CURRENT_USER)->follower();

        if ($request->search_name){
            $customers->where('username',"LIKE",'%'.$request->search_name."%");
        }

        $customers =  $customers->paginate(10);


        $result = [
            'customers'=>CustomerResource::collection($customers),
            "pagination"=>[
                "i_total_objects"=>$customers->count(),
                "i_items_on_page"=> $customers->count(),
                "i_per_pages"=>$customers->perPage(),
                "i_current_page"=>$customers->currentPage() ,
                "i_total_pages"=> $customers->total()
            ]
        ];

        return $this->sendResponse($result,'');

    }


    public function home(Request $request){

        $this->validate($request,[
            'store_id'=>'required|exists:stores,id',
        ]);

        $store = Store::find($request->store_id);

        $system = SystemDraw::where("store_id",$request->store_id)
                    ->where("is_end", false)
                    ->CanBeViewed()->orderBy( "is_end" , "asc" )->orderBy( "date" , "desc" )->first();

        $data = [];
        if ($request->offer){
            $offers =Offer::isActive()/*->hasProducts()*/
                ->where("store_id",$request->store_id)->paginate(10);
            $data =[
                'offers'=>StoreOfferResource::collection($offers),
                "pagination"=>[
                    "i_total_objects"=>$offers->count(),
                    "i_items_on_page"=> $offers->count(),
                    "i_per_pages"=>$offers->perPage(),
                    "i_current_page"=>$offers->currentPage() ,
                    "i_total_pages"=> $offers->total()
                ]

            ];
            return $this->sendResponse($data,'');
        }elseif($request->ads){
            $posts = Post::isActive()->IsNotHotSale()
                ->where("store_id",$request->store_id)
                ->orderBy( "created_at" , "desc" )
                ->paginate(12);
            $data =[
                'posts'=>PostResource::collection($posts),
                'stores'=>StoreResource::collection($this->getRecommended_stores()),
                "pagination"=>[
                    "i_total_objects"=>$posts->count(),
                    "i_items_on_page"=> $posts->count(),
                    "i_per_pages"=>$posts->perPage(),
                    "i_current_page"=>$posts->currentPage() ,
                    "i_total_pages"=> $posts->total()
                ]

            ];
            return $this->sendResponse($data,'');
        }
        $result=[
                "draw"=>[
                    "is_active"=>$system?true:false,
                    "draws"=>new SystemDrawResource($system)
                ],
                "store"=>new StoreResource($store),

            ];
        return $this->sendResponse($result,'');

    }


    public function getStores(Request $request){

        $data = Store::isActive()/*->IsNotWholeSale()*/;
        if ($request->category_id){
            $data->where('category_id',$request->category_id);
        }
        if ($request->name){
            $data->where(function ($query) use ($request){

                $query->where('name',"LIKE",'%'.$request->name."%");
                $query->orWhereHas('category',function ($query)use ($request){

                    $query->where(function ($query) use ($request){
                        foreach (getSupportedLocales() as $locale)
                            $query->orWhere("name->".$locale,"LIKE","%".$request->name."%");
                    });



                });

            });
        }
        $data = $data->paginate(12);
        $result= [
            'stores'=>StoreResource::collection($data),
            "pagination"=> [
                "i_total_objects"=>$data->count(),
                "i_items_on_page"=> $data->count(),
                "i_per_pages"=>$data->perPage(),
                "i_current_page"=>$data->currentPage() ,
                "i_total_pages"=> $data->total()
            ]
        ];
        return $this->sendResponse($result,'');
    }
//    public function getWholeSalesStores(Request $request){
//
//        $data = Store::isActive()->isWholeSale();
//        if ($request->category_id){
//            $data->where('category_id',$request->category_id);
//        }
//        if ($request->name){
//            $data->where('name',"LIKE",'%'.$request->name."%");
//        }
//        $data = $data->paginate(12);
//        $result= [
//            'stores'=>StoreResource::collection($data),
//            "pagination"=> [
//                "i_total_objects"=>$data->count(),
//                "i_items_on_page"=> $data->count(),
//                "i_per_pages"=>$data->perPage(),
//                "i_current_page"=>$data->currentPage() ,
//                "i_total_pages"=> $data->total()
//            ]
//        ];
//        return $this->sendResponse($result,'');
//    }

    public function getWholeSalesStores(Request $request){
        // Initialize the query with active and wholesale filters
        $data = Store::isActive()->isWholeSale();

        // Apply category filter if provided
        if ($request->category_id){
            $data->where('category_id', $request->category_id);
        }

        // Apply name filter if provided
        if ($request->name){
            $data->where('name', "LIKE", '%' . $request->name . "%");
        }

        // Sort by newest to oldest using created_at
        $data->orderBy('created_at', 'desc');

        // Paginate the results
        $data = $data->paginate(12);

        // Format the result for response
        $result = [
            'stores' => StoreResource::collection($data),
            "pagination" => [
                "i_total_objects" => $data->total(),
                "i_items_on_page" => $data->count(),
                "i_per_pages" => $data->perPage(),
                "i_current_page" => $data->currentPage(),
                "i_total_pages" => $data->lastPage(),
            ]
        ];

        return $this->sendResponse($result, '');
    }

    public function getRecommended_stores(){

        session(['storesSeed' => random_int(PHP_INT_MIN,PHP_INT_MAX)]);

        $data = Store::isActive()->IsWholeSale();

       return  $data->inRandomOrder(session("storesSeed"))->paginate(5);

    }

    public function getQrRequests(Request $request){
        $data = QRRequest::where('store_id',$this->CURRENT_USER->id);
        if ($request->search_name){
            $search = $request->search_name;
            $data->whereHas('name', function($q) use($search){
                $q->where('name', 'LIKE', '%'.$search.'%');
            });
        }
        $data->has("customer");
        $data = $data->orderBy("created_at","desc")->paginate(12);
        $result= [
            'qr_requests'=>SystemQrRequestResource::collection($data),
            "pagination"=> [
                "i_total_objects"=>$data->count(),
                "i_items_on_page"=> $data->count(),
                "i_per_pages"=>$data->perPage(),
                "i_current_page"=>$data->currentPage() ,
                "i_total_pages"=> $data->total()
            ]
        ];
        return $this->sendResponse($result,'');
    }

    public function changeQrRequestStatus(Request $request){
        $this->validate(
            $request,
            [
                'qr_request_id' => "required|exists:q_r_requests,id",
                'status' => "required",
            ]
        );

        $qr_request_id = QRRequest::findOrFail($request->qr_request_id);
        $qr_request_id->status = $request->status;
        $qr_request_id->save();

        if ($request->status == "Approved"){
            OrderTransaction::save($qr_request_id,"QRRequest");
        }

        CandidateTable::save($qr_request_id,"qr");
        $qr_request = QRRequest::find($qr_request_id->id);
        SendNotification::qrScanAcceptOrRejectFromStoreToCustomerNotification($qr_request);

        return $this->sendResponse($qr_request_id,'');

    }


    public function notification(Request $request){
        $currentUser = auth('store')->user();

        $notifications = $currentUser->notifications()->paginate(12);

        $currentUser->unreadNotifications->markAsRead();
        $result = [
            'notifications'=>NotificationApiResource::collection($notifications),
            "pagination"=> [
                "i_total_objects"=>$notifications->count(),
                "i_items_on_page"=> $notifications->count(),
                "i_per_pages"=>$notifications->perPage(),
                "i_current_page"=>$notifications->currentPage() ,
                "i_total_pages"=> $notifications->total()
            ]];
        return $this->sendResponse($result,null);

    }

    public function notifications_count(Request $request){

        $currentUser = auth('store')->user();
        return $this->sendResponse(["notifications_unread" =>  (int)(int)$this->CURRENT_USER->unreadNotifications->count()],null);

    }

    public function joinBlackFriday(Request $request){

        $blackFriday  = BlackFridayRequest::where([
            "store_id"=>$this->CURRENT_USER->id,
        ])->isActive($this->CURRENT_USER->id)->first();

        if ($blackFriday){
            return $this->sendError(\Lang::get("lang.already_registered"),'422');
        }


        $blackFriday =  addBlackFridaySubscription($this->CURRENT_USER , 0 );


        $setting = Setting::first();

        $view  = view("bop",
            [
                "callback" => route('bop_callback_friday'),
                "total" => $blackFriday["price"],
                "extra" => [
                    "customer_id" => $this->CURRENT_USER->id,
                    "type" => "black_friday",
                    "subscription_id" => $blackFriday->id,
                    "time_stamp" => microtime(true)
                ],
            ]
        )->render() ;


        if ($request->version == "2") {
            $res["online_payment"] = true;
            if (strtolower($this->CURRENT_USER->device_type) == "ios") {
                if (!$setting->pay_ios_hotsale) {

                    $res["online_payment"] = false;

                }
            } else {

                if (!$setting->pay_android_hotsale) {

                    $res["online_payment"] = false;
                }

            }


            $res["web"] = $res["online_payment"] ? $view : "";

            return $this->sendResponse($res, \Lang::get("lang.subscription_order_sent"));
        }else{

            return $view ;

        }

    }

    public function bop_callback(Request $request){

        file_put_contents('payment_black_friday_log_file.log',print_r($request->all(),true).'\n',FILE_APPEND);
        $data = \request()->all();
        // dd($data);

        $order = base64_decode($data['OrderID']);
        $order_data = json_decode($order);

        $ResponseCode = intval($data['ResponseCode']);
        $ReasonCode = intval($data['ReasonCode']);


        if ( $ResponseCode ===1 && $ReasonCode ===1 ){
            $blackFriday  = BlackFridayRequest::find($order_data->subscription_id);
            $blackFriday->status = 1 ;
            $blackFriday->save() ;

            return  redirect()->route('checkout.success');
        }

        return  redirect()->route('checkout.fail',["message"=>isset($data['ReasonCodeDesc'])?$data['ReasonCodeDesc']:""]); //$this->sendError("error",406,$data);

    }
    public function getBlackFridaySubscription(Request $request){

        $blackFriday  = BlackFridayRequest::where([
            "store_id"=>$this->CURRENT_USER->id,
        ])/*->isActive($this->CURRENT_USER->id)*/->orderBy("id","desc")->first();
//dd($blackFriday);
        $result = [
                    "is_active" => $blackFriday->isActive() ,
                    "is_pending" => $blackFriday->IsPending() ,
                    "price" => "0" ,
                    "currency" => "usd" ,
                    "currency_symbol" => "$" ,
                     "user_subscription" => $blackFriday?new BlackFridayRequestResource($blackFriday):null
                ];

        return $this->sendResponse($result,'');

    }


        public function sendFollowerNotification(Request $request){

                    $factory = (new Factory())->withServiceAccount( base_path(env('FIREBASE_CREDENTIALS')));
                      $storeDetails = Store::whereIn('id', [$this->CURRENT_USER->id])->get();

                    $messaging = $factory->createMessaging();
                    $customers = Follower::where('store_id', $this->CURRENT_USER->id)->pluck('customer_id')->toArray();
                    $customers1 = Customer::whereIn('id', $customers)->get();;
                    $fcmTokens = Customer::whereIn('id', $customers)->whereNotNull('fcm_token')->pluck('fcm_token')->toArray();



                $notification = Notification::create($request->title, $request->body);


          foreach ( $customers1 as $customer){

              $data=[
                  'title' =>$storeDetails->first()->name.' : '.$request->title,
                  'body' => $request->body,
                  'id'=>null,
                  'type'=>'general_notification',
                  'link'=>''
              ];

            //   fcmNotification($customer,$data);
                  \Notification::send($customer,
                  new \App\Notifications\GeneralNotification(
                     $storeDetails->first()->name.' : '.$request->title,
                      'general_notification',
                      $data
                  ));
          }



           $message = CloudMessage::new()->withNotification($notification);

            // Send notification to all FCM tokens in one statement
            $batchSize = 150; // Adjust as needed

            // Split FCM tokens into batches and send notifications
            $chunks = array_chunk($fcmTokens, $batchSize);
            foreach ($chunks as $chunk) {
                try {
                    $messaging->sendMulticast($message, $chunk);
                    // echo "Notification sent successfully to batch of FCM tokens\n";
                } catch (\Throwable $e) {
                    // Handle errors
                    // echo "Error sending notification to batch of FCM tokens. Error: ".$e->getMessage()."\n";
                }
            }

       return $this->sendResponse($customers, $fcmTokens);
        //  $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');

}



     public function bop_callback_Ads(Request $request){

        file_put_contents('payment_log_file.log',print_r($request->all(),true).'\n',FILE_APPEND);
    //     $data = \request()->all();
    //   // dd($data);


    //     $order = base64_decode($data['OrderID']);
    //     $order_data = json_decode($order);

    //     $ResponseCode = intval($data['ResponseCode']);
    //     $ReasonCode = intval($data['ReasonCode']);
    //     $PaymentStatus = $data['ReasonCodeDesc'];


        $payment = Payment::create([
            "customer_id"=>$this->CURRENT_USER->id,
            "payment_status"=>1,
            // "payload"=>json_encode($data)
        ]);


        // if ( $ResponseCode ===1 && $ReasonCode ===1 ){

if(true){

            // $this->saveAfterPaymentComeFromBop($this->CURRENT_USER->id, "teest",true);
            return  redirect()->route('checkout.success');

        }

        return  redirect()->route('checkout.fail',["message"=>isset($data['ReasonCodeDesc'])?$data['ReasonCodeDesc']:""]); //$this->sendError("error",406,$data);

    }



}
