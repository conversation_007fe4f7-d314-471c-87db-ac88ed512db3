<?php

namespace App\Http\Controllers\Store;

use App\Http\Controllers\Controller;
use App\Http\Resources\AddressResource;
use App\Http\Resources\AreaResource;
use App\Http\Resources\NotificationApiResource;
use App\Http\Resources\OffersResource;
use App\Http\Resources\SystemDrawDashboardResource;
use App\Models\Address;
use App\Models\BlackFridayRequest;
use App\Models\City;
use App\Models\Faq;
use App\Models\Governorate;
use App\Models\Offer;
use App\Models\Post;
use App\Models\Region;
use App\Models\StoreCategory;
use App\Models\SystemDraw;
use Illuminate\Http\Request;
use App\Http\Controllers\BaseControllerApi;
use Illuminate\Support\Facades\Hash;


use App\Models\Customer;

class StoreOfferApiController extends BaseControllerApi
{
    public function index(Request $request){
        $data = Offer::where('store_id',$this->CURRENT_USER->id);

        $lite   = $request->lite ? true : false ;

        if($request->search_name){
            $data->where('title',"LIKE","%".$request->search_name."%");
        }


        if (!$lite) {

                $data =  $data->paginate(12);

                $result = [
                    'offers' => OffersResource::collection($data),
                    "pagination" => [
                        "i_total_objects" => $data->count(),
                        "i_items_on_page" => $data->count(),
                        "i_per_pages" => $data->perPage(),
                        "i_current_page" => $data->currentPage(),
                        "i_total_pages" => $data->total()
                    ]

                ];

        }else{


            $blackFriday  = BlackFridayRequest::where([
                "store_id"=>$this->CURRENT_USER->id,
            ])->isActive($this->CURRENT_USER->id)->first();

            if ($blackFriday)
             $data =  $data->where("status" , true)->orWhere("is_black",true)->get();
            else
            $data =  $data->where("status" , true)->get();


            $result= [
                    'offers'=> $data,
                ];

        }


        return $this->sendResponse($result,'');

    }

    public function store(Request $request){
        $this->validate($request, [
                'title' => 'required',

            ]);
        $filename = $request->image->store('/','public');

        $create = Offer::create([
            "title"=>$request->title,
            "store_id"=>$this->CURRENT_USER->id,
            "start_date"=>$request->start_date,
            "end_date"=>$request->end_date,
            "status"=>$request->status =="true"?true:false,
            "image"=>$filename,

            ]);
        return $this->sendResponse($create,'');
    }

    public function update(Request $request){

        $this->validate($request, [
            "offer_id" =>"required|exists:offers,id",
            'title' => 'required',

        ]);
        $offer = Offer::where("id",$request->offer_id)
            ->where("store_id",$this->CURRENT_USER->id)->first();
        if (is_null($offer)){
            return $this->sendError(\Lang::get("lang.no_permissions"),422);
        }
        $filename =  $request->image ?$request->file("image")
            ->store('/','public'):$offer->image;

        $offer->update([
            "title"=>$request->title,
            "store_id"=>$this->CURRENT_USER->id,
            "start_date"=>$request->start_date,
            "end_date"=>$request->end_date,
            "status"=>$request->status =="true"?true:false,
            "image"=>$filename,

        ]);

        return $this->sendResponse($offer,'');
    }

    public function delete(Request $request){
        $this->validate(
            $request,
            [
                'id' => "required|exists:offers,id",
            ]
        );

        $offer = Offer::where("id",$request->id)
            ->where("store_id",$this->CURRENT_USER->id)->first();
        if (is_null($offer)){
            return $this->sendError(\Lang::get("lang.no_permissions"),422);
        }
        $offer->delete();
        return $this->sendResponse(null,\Lang::get("no_permissions"));
    }

}
