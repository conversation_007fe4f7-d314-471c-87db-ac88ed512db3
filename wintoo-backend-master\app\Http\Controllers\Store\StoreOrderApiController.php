<?php

namespace App\Http\Controllers\Store;

use App\Http\Controllers\Controller;
use App\Http\Resources\AreaResource;
use App\Http\Resources\NotificationApiResource;
use App\Http\Resources\OrderResource;
use App\Http\Resources\OrderTrackerResource;
use App\Http\Resources\PostResource;
use App\Http\Resources\StoreCategoryResource;
use App\Http\Resources\StoreResource;
use App\Models\City;
use App\Models\Faq;
use App\Models\Governorate;
use App\Models\Order;
use App\Models\Post;
use App\Models\Region;
use App\Models\Store;
use App\Models\StoreCategory;
use App\Services\Delivery;
use App\Services\OrderTrack;
use App\Services\SendNotification;
use Illuminate\Http\Request;
use App\Http\Controllers\BaseControllerApi;
use Illuminate\Support\Facades\Hash;


use App\Models\Customer;

class StoreOrderApiController extends BaseControllerApi
{
    public function getOrders(Request $request){

        $data = Order::where('store_id',$this->CURRENT_USER->id)
            ->orderBy('created_at','desc');

        $data->has("order_items");
        $data->has("customer");
        // dd($request->order_status);
        if($request->has('order_status')){
            $data->where('status',$request->order_status);
        }
        if($request->has('name')){
            $search = $request->name;
            $data->whereHas('customer', function($q) use($search){
                $q->where('username', 'LIKE', '%'.$search.'%');
            });
            //$data->where('store',$request->name);
        }

        $data = $data->paginate(12);

        //  dd($data);
        $result= [
            'order'=>OrderResource::collection($data),
            "pagination"=> [
                "i_total_objects"=>$data->count(),
                "i_items_on_page"=> $data->count(),
                "i_per_pages"=>$data->perPage(),
                "i_current_page"=>$data->currentPage() ,
                "i_total_pages"=> $data->total()
            ]

        ];

        return $this->sendResponse($result,'');


    }

    public function getOrderDetails(Request $request){

        $data = Order::find($request->order_id);
        $order_trackers = $data->order_trackers()->orderBy("created_at","desc")->get();
        $result= [
             'order'=>new OrderResource($data),
             "order_track" =>OrderTrackerResource::collection
             ($order_trackers),
        ];
        return $this->sendResponse($result,'');
    }


    public function statusChange(Request $request){
        $this->validate($request,[
            'order_id'=>'required',
        ]);

        $data = Order::where('id',$request->order_id)
            ->where('store_id',$this->CURRENT_USER->id)
            ->first();
        if(is_null($data)){
            return $this->sendError(\Lang::get("lang.can_not_cancel_order"),422);
        }
        $data->status = $request->status;
        $data->save();
        $data = Order::where('id',$request->order_id)->where('store_id',$this->CURRENT_USER->id)->first();

        OrderTrack::trackerSave($data);
        $order = Order::find($data->id);

        SendNotification::storeChangeOrderStatus($order);

        if ($data->status == 5 && is_null($order->delivery_reference_id) ){
            $delivery_service = new Delivery();
            $delivery_status =  $delivery_service->create_order($order);

            if (!$delivery_status){

                return $this->sendError(\Lang::get("lang.failed_to_send_to_delivery_company"),422);

            }
        }elseif ($data->status == 5 && !is_null($order->delivery_reference_id)){

            $this->showModal('حصل خطأ ما','تم الإرسال مسبقا لشركة التوصيل','error');
            return $this->sendError(\Lang::get("lang.sent_before_to_delivery"),422);

        }


        $order_trackers = $data->order_trackers()->orderBy("created_at","desc")->get();
        $result= [
            'order'=>new OrderResource($data),
            "order_track" =>OrderTrackerResource::collection
            ($order_trackers),
        ];
        return $this->sendResponse($result,'');
    }

    public function olivery_order_updates(Request $request){

        $request->merge(["datetime" => date("Y-m-d H:i:s")]);
        file_put_contents('olivery_log_file.log',print_r($request->all(),true).'\n',FILE_APPEND);

        $order = Order::where("delivery_reference_id",$request->order_id);

        if (in_array($request->status,["in_progress","delivered","completed"])) {
            if ($request->status == "in_progress") {

                $order->status = 2;

            } elseif ($request->status == "delivered") {

                $order->status = 3;

            } elseif ($request->status == "completed") {
                $order->status = 4;

            }

            SendNotification::storeChangeOrderStatus($order);

        }

        return $this->sendResponse(null,'succeed');


    }

}
