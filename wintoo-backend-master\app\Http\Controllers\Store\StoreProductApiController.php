<?php

namespace App\Http\Controllers\Store;

use App\Http\Controllers\Controller;
use App\Http\Resources\AddressResource;
use App\Http\Resources\AreaResource;
use App\Http\Resources\NotificationApiResource;
use App\Http\Resources\OffersResource;
use App\Http\Resources\ProductStoreResource;
use App\Http\Resources\SystemDrawDashboardResource;
use App\Models\Address;
use App\Models\City;
use App\Models\Faq;
use App\Models\Governorate;
use App\Models\Offer;
use App\Models\Post;
use App\Models\Product;
use App\Models\ProductImage;
use App\Models\Region;
use App\Models\StoreCategory;
use App\Models\SystemDraw;
use App\Models\Variation;
use Illuminate\Http\Request;
use App\Http\Controllers\BaseControllerApi;
use Illuminate\Support\Facades\Hash;


use App\Models\Customer;
use Illuminate\Support\Str;

class StoreProductApiController extends BaseControllerApi
{
    public function index(Request $request){


        $data = Product::where('store_id',$this->CURRENT_USER->id);

//   return $this->sendResponse($result,'dd');
        if($request->search_name){
            $data->where('title',"LIKE","%".$request->search_name."%");
        }


        if($request->order_by =='best_seller'){

            $data->orderBy('sell_number','desc');
        }else
        if($request->order_by =='highest_price' ){
            $data ->orderByRaw('IF( products.offer_id is not null  and  products.new_price > 0 , products.new_price ,  products.price) DESC');
            //  $products->orderBy('price','desc');
        }else
        if($request->order_by =='lowest_price' ){
            //   $products ->orderByRaw('IFNULL(products.new_price, products.price) ASC');
            $data ->orderByRaw('IF(products.offer_id is not null  and  products.new_price > 0 , products.new_price ,  products.price) ASC');

            // $products->orderBy('price','asc');

        }
        else
         $data->orderBy("created_at","desc");

        $data =   $data->paginate(12);


        $result= [
            'products'=>ProductStoreResource::collection($data),
            "video_url"=> Faq::find(3)->video_url ,
            "pagination"=> [
                "i_total_objects"=>$data->count(),
                "i_items_on_page"=> $data->count(),
                "i_per_pages"=>$data->perPage(),
                "i_current_page"=>$data->currentPage() ,
                "i_total_pages"=> $data->total()
            ]
        ];
        return $this->sendResponse($result,'');
    }

    public function store(Request $request){

        $this->validate($request, [

            'title' => 'required',
          //  'category_id' => 'required',
            //'sub_category_id' => 'required',
            'main_category_id' => 'required',
          //  'currency_id' => 'required',
       //     'units' => 'required|array',
        //    'units.*.display_selling_price' => 'required|numeric',
            'units.*.product_size' => 'required',
            'units.*.product_color' => 'required',
            'price' => 'required'

        ],[],[
            "price" => \Lang::get("lang.product_price"),
            "units.*.price" => \Lang::get("lang.product_unit_price"),
            "units.*.display_selling_price" => \Lang::get("lang.product_unit_price")
        ]);




        $new_price=null;
        $discount_amount=null;
        if ($request->is_offer =="true"){
        if($request->offer_discount_type === 'fixed'){
            $new_price= $request->price - $request->percentage_value;
            $discount_amount=$request->percentage_value;
        }
        if($request->offer_discount_type === 'percentage'){
            $discount_amount=($request->price * ($request->percentage_value / 100));
            $new_price=$request->price-($request->price * ($request->percentage_value / 100));
        }
        }

        $product_create = Product::create([
            'title'=>$request->title,
            'category_id'=>$request->category_id,
            'sub_category_id'=>$request->sub_category_id,
            'main_category_id'=>$request->main_category_id,
            'store_id'=>$this->CURRENT_USER->id,
            'offer_id'=>$request->offer_id,
            'currency_id'=>$request->currency_id,
            'brand_id'=>$request->brand_id,
            'description'=>$request->description,
            'price'=>(double)$request->price,
            'stock_quantity'=>(integer)$request->stock_quantity,
            'new_price'=>(double)$new_price,
            'discount_amount'=>$discount_amount,
            'offer_discount_type'=>$request->offer_discount_type,
            'percentage_value'=>$request->percentage_value,
            'is_new'=>$request->is_new,
            'is_available'=>$request->is_available,
            'bar_code'=>$request->bar_code,
            'profit_type'=>$request->profit_type,
            'profit_amount'=>$request->profit_amount,
            'identifier'=>$request->identifier,
            'is_hotsale'=>(boolean)$request->is_hotsale,
        ]);


        if($request->file('main_image')){
            $filename = $request->file('main_image')->store('/','product');
            $product_create->product_images()->create([
                'image' =>$filename ,
                'is_main'=>true,
            ]);
        }
        foreach ($request->images as $image){
            $filename = $image->store('/','product');
            $product_create->product_images()->create([
                'image' =>$filename ,
            ]);
        }

            foreach ($request->units as $unit){


                $unit['display_selling_price'] = !isset($unit['display_selling_price']) || $unit['display_selling_price']=="null" ||  !$unit['display_selling_price'] ?(double)$request->price:(double)$unit['display_selling_price'];

                if ($request->identifier == 'لون فقط'){
                    $unit['product_size'] = null;
                }
                //create size
                $find_size=\App\Models\Size::where('size_ar',$unit['product_size'])->first();


                if($find_size){
                    $size=$find_size;
                }else{
                    $size= \App\Models\Size::firstOrCreate([
                        'size_ar' => $unit['product_size'],
                    ]);
                }

                //create color

                if(empty($unit['product_color'])){
                    $near_color='without color';

                    $main_color= \App\Models\Maincolor::find(23);
                }else{
                    $main_color= \App\Models\Maincolor::where('id',$unit['product_color'])->first();


                }

                $color= \App\Models\Color::firstOrCreate([
                    'name' => $main_color->name,
                    'color_ar' => $main_color->human_name,
                    'code' => $main_color->code,
                    'human_name' => $main_color->human_name,
                    'main_color_id' =>$main_color->id,
                ]);

                $variation_price_after_offer= null;
                $variation_offer_type=null;
                $offer_price=null;
                //find variation
                if ($request->is_offer =="true") {

                    if ($request->offer_discount_type === 'fixed') {

                        $variation_price_after_offer = $unit['display_selling_price'] - $request->percentage_value;
                        $variation_offer_type = 'fixed';
                        $offer_price = $request->percentage_value;
                    }
                    if ($request->offer_discount_type === 'percentage') {

                        $variation_price_after_offer = $unit['display_selling_price'] - ($unit['display_selling_price'] * ($request->percentage_value / 100));
                        $variation_offer_type = 'percentage';
                        $offer_price = $unit['display_selling_price'] * ($request->percentage_value / 100);
                    }
                }

                Variation::create([
                    'product_id'=>$product_create->id,
                    'category_id'=>$product_create->category_id,
                    'product_size_id'=>$size->id,
                    'product_color_id'=>$main_color->id,
                    'is_in_stock'=>$unit['is_in_stock']?"Yes":"No",
                    'stock_quantity'=>$unit['stock_quantity'],
                    'display_selling_price'=>$unit['display_selling_price'],
                    'offer_type'=>$variation_offer_type,
                    'price_after_offer'=>$variation_price_after_offer,
                    'offer_price'=>$offer_price,
                ]);
//                    }

            }


        return $this->sendResponse(new ProductStoreResource(Product::find($product_create->id)),'');

    }

    public function update(Request $request){

        $this->validate($request, [

            "product_id" =>"required|exists:products,id",
            'title' => 'required',
           // 'category_id' => 'required',
      //      'sub_category_id' => 'required',
            'main_category_id' => 'required',
          //  'units' => 'required|array',
          //  'units.*.display_selling_price' => 'required|numeric',
            'units.*.product_size' => 'required',
            'units.*.product_color' => 'required',
            'price' => 'required'

        ],[],[
            "price" => \Lang::get("lang.product_price"),
            "units.*.price" => \Lang::get("lang.lang.product_unit_price")
        ]);
        $product = Product::where("id",$request->product_id)
            ->where("store_id",$this->CURRENT_USER->id)->first();
        if (is_null($product)){
            return $this->sendError(\Lang::get("lang.can_not_cancel_order"),422);
        }


        $new_price=null;
        $discount_amount=null;
        if ($request->is_offer =="true") {

            if ($request->offer_discount_type === 'fixed') {
                $new_price = $request->price - $request->percentage_value;
                $discount_amount = $request->percentage_value;
            }
            if ($request->offer_discount_type === 'percentage') {
                $discount_amount = ($request->price * ($request->percentage_value / 100));
                $new_price = $request->price - ($request->price * ($request->percentage_value / 100));
            }
        }

        $product->update([
            'title'=>$request->title,
            'category_id'=>$request->category_id,
            'sub_category_id'=>$request->sub_category_id,
            'main_category_id'=>$request->main_category_id,
            'offer_id'=>$request->offer_id,
            'brand_id'=>$request->brand_id,
            'currency_id'=>$request->currency_id,

            'description'=>$request->description,
            'price'=>$request->price,
            'stock_quantity'=>(integer)$request->stock_quantity,
            'new_price'=>$new_price,
            'discount_amount'=>$discount_amount,
            'offer_discount_type'=>$request->offer_discount_type,
            'percentage_value'=>$request->percentage_value,
            'is_new'=>$request->is_new,
            'is_available'=>$request->is_available,
            'bar_code'=>$request->bar_code,
            'profit_type'=>$request->profit_type,
            'profit_amount'=>$request->profit_amount,
            'identifier'=>$request->identifier,
            'is_hotsale'=>(boolean)$request->is_hotsale,

        ]);


        if($request->file('main_image')){
            $filename = $request->file('main_image')->store('/','product');
            $product->product_images()->create([
                'image' =>$filename ,
                'is_main'=>true,
            ]);
        }
        foreach ($request->images as $image){
            $filename = $image->store('/','product');
            $product->product_images()->create([
                'image' =>$filename ,
            ]);
        }

        foreach ($request->units as $unit){

            $unit['display_selling_price'] = !isset($unit['display_selling_price']) || $unit['display_selling_price']=="null" ||  !$unit['display_selling_price'] ?(double)$request->price:(double)$unit['display_selling_price'];

            if ($request->identifier == 'لون فقط'){
                $unit['product_size'] = null;
            }
            //create size
            $find_size=\App\Models\Size::where('size_ar',$unit['product_size'])->first();


            if($find_size){
                $size=$find_size;
            }else{
                $size= \App\Models\Size::firstOrCreate([
                    'size_ar' => $unit['product_size'],
                ]);
            }

            //create color

            if(empty($unit['product_color'])){
                $near_color='without color';

                $main_color= \App\Models\Maincolor::find(23);
            }else{
                $main_color= \App\Models\Maincolor::where('id',$unit['product_color'])->first();


            }

            $color= \App\Models\Color::firstOrCreate([
                'name' => $main_color->name,
                'color_ar' => $main_color->human_name,
                'code' => $main_color->code,
                'human_name' => $main_color->human_name,
                'main_color_id' =>$main_color->id,
            ]);

            $variation_price_after_offer= null;
            $variation_offer_type=null;
            $offer_price=null;
            //find variation
            if ($request->is_offer =="true") {

                if ($request->offer_discount_type === 'fixed') {

                    $variation_price_after_offer = $unit['display_selling_price'] - $request->percentage_value;
                    $variation_offer_type = 'fixed';
                    $offer_price = $request->percentage_value;
                }
                if ($request->offer_discount_type === 'percentage') {

                    $variation_price_after_offer = $unit['display_selling_price'] - ($unit['display_selling_price'] * ($request->percentage_value / 100));
                    $variation_offer_type = 'percentage';
                    $offer_price = $unit['display_selling_price'] * ($request->percentage_value / 100);
                }

            }

        $find_variation = Variation::find($unit["id"]);

        if($find_variation){

            $find_variation->update([
                'product_id'=>$product->id,
                'category_id'=>$product->category_id,
                'product_size_id'=>$size->id,
                'product_color_id'=>$main_color->id,
                'is_in_stock'=>$unit['is_in_stock']=="true"?"Yes":"No",
                'status'=>$unit['status']=="true"?true:false,
                'stock_quantity'=>$unit['stock_quantity'],
                'display_selling_price'=>$unit['display_selling_price'],
                'offer_type'=>$variation_offer_type,
                'price_after_offer'=>$variation_price_after_offer,
                'offer_price'=>$offer_price,
            ]);

        }else{
            Variation::create([
                'product_id'=>$product->id,
                'category_id'=>$product->category_id,
                'product_size_id'=>$size->id,
                'product_color_id'=>$main_color->id,
                'is_in_stock'=>$unit['is_in_stock']=="true"?"Yes":"No",
                'stock_quantity'=>$unit['stock_quantity'],
                'display_selling_price'=>$unit['display_selling_price'],
                'offer_type'=>$variation_offer_type,
                'price_after_offer'=>$variation_price_after_offer,
                'offer_price'=>$offer_price,
                'status'=>$unit['status']=="true"?true:false,

            ]);
        }

//                    }

        }


        return $this->sendResponse(new ProductStoreResource(Product::find($product->id)),'');


    }

    public function delete(Request $request){
        $this->validate(
            $request,
            [
                'id' => "required|exists:products,id",
            ]
        );

        $product = Product::where("id",$request->id)
            ->where("store_id",$this->CURRENT_USER->id)->first();
        if (is_null($product)){
            return $this->sendError(\Lang::get("lang.no_permissions"),422);
        }
        $product->delete();
        return $this->sendResponse(null,\Lang::get("lang.deleted_successfully"));
    }

    public function imageDelete(Request $request){
        $this->validate(
            $request,
            [
                'product_id' => "required|exists:products,id",
                'image_id' => "required",

            ]
        );

        $product = ProductImage::where("id",$request->image_id)
            ->where("product_id",$request->product_id)
          /*  ->where("store_id",$this->CURRENT_USER->id)*/->first();
        if (is_null($product)){
            return $this->sendError(\Lang::get("lang.no_permissions"),422);
        }
        $product->delete();
        return $this->sendResponse(null,\Lang::get("lang.deleted_successfully"));
    }
    public function variationDelete(Request $request){
        $this->validate(
            $request,
            [
                'product_id' => "required|exists:products,id",
                'variation_id' => "required",

            ]
        );

        $product = Variation::where("id",$request->variation_id)
            ->where("product_id",$request->product_id)
            ->first();
        if (is_null($product)){
            return $this->sendError(\Lang::get("lang.no_permissions"),422);
        }
        $product->delete();
        return $this->sendResponse(null,\Lang::get("lang.deleted_successfully"));
    }



}
