<?php

namespace App\Http\Livewire;

use Livewire\Component;
use App\Traits\PublicFunction;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use App\Traits\Alert;
use App\Models\StoreCategory;
class StoreCategoryFormLivewire extends Component
{
     use WithFileUploads,Alert,PublicFunction;
      public $title = "";
      public $file_name = 'storeCategory';
      public $image ;


      public $storeCategory;
      protected $listeners = ['StoreCategory-livewire:conformDelete' => 'conformDelete','reviewSectionRefresh' => '$refresh',];

        protected function rules()
        {
            return [
                "storeCategory.icon"=>'required',
                "storeCategory.name.ar"=>'required',
                "storeCategory.name.tr"=>'required',
                "storeCategory.name.en"=>'required',
                "storeCategory.name.he"=>'required',
                "storeCategory.status"=>'nullable',
                "storeCategory.order"=>'required|numeric|unique:store_categories,order,'.$this->storeCategory->id,
            ];
        }

       protected $validationAttributes;
       public function __construct($id = null)
       {
           parent::__construct($id);
           $this->validationAttributes = $this->getColNameForValidation(StoreCategory::getColumnLang());
       }
      public function mount($id =null)
          {
              $this->title = \Lang::get('lang.add_account')  ;
              $this->storeCategory  = $id?StoreCategory::find($id):new StoreCategory();
          }
      public function render()
          {
              return view('dashboard/storeCategory/form')->extends('dashboard_layout.main');
          }

      public function save(){
           // $this->validate();
           \DB::beginTransaction();
           try {

               $filename = $this->image?$this->image->store('/','public'):$this->storeCategory->icon;
               $this->storeCategory->icon=$filename;
               $this->storeCategory->save();

                \DB::commit();
               $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');
               return redirect()->route('dashboard.storeCategory');
            } catch (\Exception $e) {
                    \DB::rollback();
                    $this->showModal('حصل خطأ ما',$e->getMessage(),'error');
            }
       }

}


