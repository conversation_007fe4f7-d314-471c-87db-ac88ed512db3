<?php

namespace App\Http\Livewire;

use App\Models\Store;
use App\Models\SubCategory;
use Livewire\Component;
use App\Traits\PublicFunction;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use App\Traits\Alert;
use App\Models\StoreCategory;
class StoreCategoryLivewire extends Component
{
     use WithFileUploads,Alert,PublicFunction,WithPagination;
        public $columes;
        public $searchable;
        public $page_length = 10;
        public $sortBy="created_at";
        public $sortDirection="desc";
        protected $paginationTheme = 'bootstrap';
        public  $search_array=[];
        protected $listeners = ['StoreCategory-livewire:conformDelete' => 'conformDelete','reviewSectionRefresh' => '$refresh',];
        public $file_name = 'storeCategory';
        public function mount()
            {
                $searchable = StoreCategory::getSearchable();
                $this->searchable =collect($searchable);
                $this->columes =StoreCategory::getColumnLang();
                $this->searchable =StoreCategory::getSearchable();
                $this->page_length = request()->query('page_length',$this->page_length);
            }


        public function render()
           {
               $data =StoreCategory::search($this->search_array);
               $data=$data->orderBy("order","asc")->paginate($this->page_length);
               return view('dashboard/storeCategory/index',[ 'data'=>$data])->extends('dashboard_layout.main');

           }

        public function search(){
                    $this->resetPage();
        }

        public function resetSearch(){
            $this->search_array=[];
         }

        public function edit($id){
                 return redirect()->route('dashboard.storeCategory.edit',$id);
             }

    public function delete($id){

     $this->showConfirmation(\Lang::get('lang.confirm_delete'),\Lang::get('lang.please_confirm_delete'),'StoreCategory-livewire:conformDelete',['id'=>$id]);

    }


    public function conformDelete($id){

        $object = StoreCategory::whereNot("type","wholesale")->where($id['id'])->first();
        $getStoreConnectedToCategory = Store::where("category_id",$object->id)->get();
            if($getStoreConnectedToCategory->isNotEmpty()){
                $this->showModal("خطأ ","هذا التنصيف مرتبط بمتاجر الرجا حذف المتاجر اولا.",'error');
                return true;
            }
                 StoreCategory::find($id['id'])->delete();

                 $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');

             }
    public function setStatus($id){
        $object = StoreCategory::find($id);
        $object->status =!$object->status;
        $object->save();
    }
}

