<?php

namespace App\Http\Livewire;

use App\Models\City;
use App\Models\Governorate;
use App\Models\Region;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Livewire\Component;
use App\Traits\PublicFunction;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use App\Traits\Alert;
use App\Models\Store;
class StoreFormLivewire extends Component
{
     use WithFileUploads,Alert,PublicFunction;
      public $title = "";
      public $file_name = 'store';
      public $store;
      public $image;
      public $days = [
             ["id"=>1,"name"=>"السبت",  "is_selected"=>true],
             ["id"=>2,"name"=>"الاحد",   "is_selected"=>true],
             ["id"=>3,"name"=>"الاثنين", "is_selected"=>true],
             ["id"=>4,"name"=>"الثلاثاء","is_selected"=>true],
             ["id"=>5,"name"=>"الاربعاء","is_selected"=>true],
             ["id"=>6,"name"=>"الخميس", "is_selected"=>true],
             ["id"=>7,"name"=>"الجمعة", "is_selected"=>true],
      ];
      public $regions=[];
      public $cities=[];
      public $governorates=[];
      public $password;
      public $password_confirmation;
      protected $listeners = ['Store-livewire:conformDelete' => 'conformDelete','reviewSectionRefresh' => '$refresh',];

    protected function rules()
    {
        $rules = [
            "store.name"=>'required',
            "store.bio"=>'nullable',
            "store.logo"=>'nullable',
            "store.mobile"=>'required|digits:10|regex:/(05)[0-9]{8}/|unique:stores,mobile,'.$this->store->id,
            "store.phone"=>'nullable',
            "store.email"=>'required|email|unique:stores,email,'.$this->store->id,
            "store.address"=>'required',
            "store.category_id"=>'required',
            "store.open_time_from"=>'nullable',
            "store.open_time_to"=>'nullable',
            "store.status"=>'nullable',
            "store.open_days"=>'nullable',
            "store.city_id"=>'nullable',
            "store.governorate_id"=>'nullable',
            "store.region_id"=>'nullable',
            "store.whatsapp"=>'nullable',
            "store.facebook"=>'nullable',
            "store.instagram"=>'nullable',
            "store.open_type"=>'nullable',
            "store.is_black_allow"=>'nullable',
            "store.country_id"=>'nullable',
        ];
        if (is_null($this->store->id)){
            $rules["password"] = 'required|confirmed';
        }

        return $rules ;
    }
       protected $validationAttributes;
       public function __construct($id = null)
       {
           parent::__construct($id);
           $this->validationAttributes = $this->getColNameForValidation(Store::getColumnLang());
       }
       public function mount($id =null)
          {
              $this->title = \Lang::get('lang.add_account')  ;
              $this->store  = $id?Store::find($id):new Store();

              if ($this->store){
                  $this->days = json_decode($this->store->open_days) ?json_decode($this->store->open_days):$this->days ;
                  $this->cities =  City::where("governorate_id",$this->store->governorate_id)->get();
                  $this->governorates =  Governorate::where("country_id",$this->store->country_id)->get();
                  $this->regions =  Region::where("city_id",$this->store->city_id)->get();

              }

          }
       public function render()
          {
              return view('dashboard/store/form')->extends('dashboard_layout.main');
          }
       public function save(){

         //   dd($this->store);

            $this->validate();
           \DB::beginTransaction();
           try {
               $filename = $this->image?$this->image->store('/','public'):$this->store->logo;
               $this->store->logo=$filename;

               if ($this->password){
                   $this->store->password = Hash::make($this->password);
               }

               $this->store->status = $this->store->status ? true:false;
               $this->store->open_days = json_encode($this->days);
               $this->store->is_black_allow = $this->store->is_black_allow?$this->store->is_black_allow:false;
               $this->store->save();
                \DB::commit();
               $this->createUsers();
               $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');
               return redirect()->route('dashboard.store');
            } catch (\Exception $e) {
                    \DB::rollback();
                    $this->showModal('حصل خطأ ما',$e->getMessage(),'error');
            }
       }
       public function createUsers(){
          $update = User::where("store_id",$this->store->id)->first();
           if ($update){

               if ($this->password){
                   $update->password = Hash::make($this->password);
                   $update->save();
               }
           }else{
               $create = User::create([
                   'email'=>$this->store->email,
                   'name'=>$this->store->name,
                   'password'=>  $this->store->password,
                   'role_id' =>1,
                   'type' =>"STORE",
                   'store_id' =>$this->store->id]);
           }

      }
      public function updated($name){
        if($name == "store.country_id"){
            $governorates = Governorate::where('country_id',$this->store->country_id)->get();
            $this->emit('testListener',["id"=>"governorate_id","data"=>$governorates]);
        }
        if($name == "store.governorate_id"){
            $governorates = City::where('governorate_id',$this->store->governorate_id)->get();
            $this->emit('testListener',["id"=>"city_id","data"=>$governorates]);
        }
        if($name == "store.city_id"){
          $regoins = Region::where('city_id',$this->store->city_id)->get();
          $this->emit('testListener',["id"=>"region_id","data"=>$regoins]);
      }
    }

}


