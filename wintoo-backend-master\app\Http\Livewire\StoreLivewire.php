<?php

namespace App\Http\Livewire;

use App\Models\StoreCategory;
use App\Models\User;
use Livewire\Component;
use App\Traits\PublicFunction;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use App\Traits\Alert;
use App\Models\Store;
class StoreLivewire extends Component
{
     use WithFileUploads,Alert,PublicFunction,WithPagination;
        public $columes;
        public $searchable;
        public $page_length = 10;
        public $sortBy="created_at";
        public $sortDirection="desc";
        protected $paginationTheme = 'bootstrap';
        public  $search_array=[];
        protected $listeners = ['Store-livewire:conformDelete' => 'conformDelete','reviewSectionRefresh' => '$refresh',];
        public $file_name = 'store';
        public function mount()
            {
                $searchable = Store::getSearchable();
                $this->searchable =collect($searchable);
                $this->columes =Store::getColumnLang();
                $this->searchable =Store::getSearchable();
                $this->page_length = request()->query('page_length',$this->page_length);
            }


        public function render()
           {
               $data =Store::search($this->search_array);
               $data=$data->orderBy($this->sortBy,$this->sortDirection)->paginate($this->page_length);
               return view('dashboard/store/index',[ 'data'=>$data])->extends('dashboard_layout.main');

           }

        public function search(){
                    $this->resetPage();
        }
         public function resetSearch(){
            $this->search_array=[];
         }

    public function edit($id){
         return redirect()->route('dashboard.store.edit',$id);
     }

     public function delete($id){
         $this->showConfirmation(\Lang::get('lang.confirm_delete'),\Lang::get('lang.please_confirm_delete'),'Store-livewire:conformDelete',['id'=>$id]);
     }

     public function conformDelete($id){

         Store::find($id['id'])->delete();

         $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');

     }

    public function setStatus($id){
        $object = Store::find($id);
        $object->status =!$object->status;
        $object->save();
        $user = User::where("store_id",$id)->first();
        $user->status = !$user->status;
        $user->save();
    }

    public  function LoginAsStore($id){
        $store = Store::find($id);

        return redirect()->route("loginById",$store->user->id);
    }


}

