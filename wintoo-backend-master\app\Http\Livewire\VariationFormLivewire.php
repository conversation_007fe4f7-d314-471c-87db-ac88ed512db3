<?php

namespace App\Http\Livewire;

use Livewire\Component;
use App\Traits\PublicFunction;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use App\Traits\Alert;
use App\Models\Variation;
class VariationFormLivewire extends Component
{
     use WithFileUploads,Alert,PublicFunction;
      public $title = "";
      public $file_name = 'Variation';
      public $Variation;
      protected $listeners = ['Variation-livewire:conformDelete' => 'conformDelete','reviewSectionRefresh' => '$refresh',];
       protected $rules = [
                "Variation.product_id"=>'nullable',
"Variation.variation_id"=>'nullable',
"Variation.name"=>'nullable',
"Variation.is_in_stock"=>'nullable',
"Variation.product_size"=>'nullable',
"Variation.product_color"=>'nullable',
"Variation.stock_code"=>'nullable',
"Variation.currency"=>'nullable',
"Variation.selling_price"=>'nullable',
"Variation.vat_included"=>'nullable',
"Variation.vat_percent"=>'nullable',
"Variation.stock_quantity"=>'nullable',
"Variation.specific_sale_price"=>'nullable',
"Variation.display_selling_price"=>'nullable',
"Variation.store_selling_price"=>'nullable',
"Variation.display_symbol"=>'nullable',
"Variation.display_currency"=>'nullable',
"Variation.color_en"=>'nullable',
"Variation.size_en"=>'nullable',
"Variation.offer_price"=>'nullable',
"Variation.offer_type"=>'nullable',
"Variation.price_after_offer"=>'nullable',

       ];

       protected $validationAttributes;
       public function __construct($id = null)
       {
           parent::__construct($id);
           $this->validationAttributes = $this->getColNameForValidation(Variation::getColumnLang());
       }
      public function mount($id =null)
          {
              $this->title = \Lang::get('lang.add_account')  ;
              $this->Variation  = $id?Variation::find($id):new Variation();
          }
      public function render()
          {
              return view('dashboard/Variation/form')->extends('dashboard_layout.main');
          }

      public function save(){
            $this->validate();
           \DB::beginTransaction();
           try {
               $this->Variation->save();
                \DB::commit();
               $this->showModal(\Lang::get('lang.saved_done'),\Lang::get('lang.saved_changed'),'success');
               return redirect()->route('dashboard.Variation');
            } catch (\Exception $e) {
                    \DB::rollback();
                    $this->showModal('حصل خطأ ما',$e->getMessage(),'error');
            }
       }

}


